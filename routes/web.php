<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UrlController;
use App\Http\Controllers\RedirectController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\QrCodeController;

// Public routes
Route::get('/', [UrlController::class, 'index'])->name('home');

// URL shortening routes
Route::post('/shorten', [UrlController::class, 'store'])->name('urls.store');
Route::post('/bulk-shorten', [UrlController::class, 'bulkStore'])->name('urls.bulk-store');

// QR Code routes (public)
Route::get('/{code}/qr', [QrCodeController::class, 'show'])->name('qr.show');
Route::get('/{code}/qr/download', [QrCodeController::class, 'download'])->name('qr.download');

// URL redirection routes
Route::get('/{code}', [RedirectController::class, 'redirect'])->name('redirect');
Route::get('/{code}/preview', [RedirectController::class, 'preview'])->name('urls.preview');

// Authentication routes
Auth::routes(['verify' => true]);

// Protected routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/analytics', [DashboardController::class, 'analytics'])->name('dashboard.analytics');

    // URL management
    Route::get('/urls/{url}', [UrlController::class, 'show'])->name('urls.show');
    Route::get('/urls/{url}/edit', [UrlController::class, 'edit'])->name('urls.edit');
    Route::put('/urls/{url}', [UrlController::class, 'update'])->name('urls.update');
    Route::delete('/urls/{url}', [UrlController::class, 'destroy'])->name('urls.destroy');
    Route::get('/urls/{url}/analytics', [UrlController::class, 'analytics'])->name('urls.analytics');

    // QR Code management
    Route::get('/urls/{url}/qr/customize', [QrCodeController::class, 'customize'])->name('qr.customize');
    Route::post('/urls/{url}/qr/generate', [QrCodeController::class, 'generate'])->name('qr.generate');
    Route::post('/urls/{url}/qr/generate-logo', [QrCodeController::class, 'generateWithLogo'])->name('qr.generate-logo');
    Route::post('/urls/{url}/qr/generate-multiple', [QrCodeController::class, 'generateMultiple'])->name('qr.generate-multiple');
    Route::delete('/urls/{url}/qr', [QrCodeController::class, 'destroy'])->name('qr.destroy');
});

// Redirect legacy home route
Route::get('/home', function () {
    return redirect()->route('dashboard');
});
