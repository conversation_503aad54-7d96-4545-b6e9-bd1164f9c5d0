<?php

namespace App\Http\Controllers;

use App\Models\Url;
use App\Services\AnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Hash;

class RedirectController extends Controller
{
    private AnalyticsService $analytics;

    public function __construct(AnalyticsService $analytics)
    {
        $this->analytics = $analytics;
    }

    /**
     * Handle URL redirection.
     */
    public function redirect(Request $request, string $code): RedirectResponse|View
    {
        // Find URL by short code or custom alias
        $url = Url::where('short_code', $code)
            ->orWhere('custom_alias', $code)
            ->where('is_active', true)
            ->first();

        if (!$url) {
            abort(404, 'Short URL not found.');
        }

        // Check if URL is expired
        if ($url->isExpired()) {
            abort(410, 'This short URL has expired.');
        }

        // Check if URL is password protected
        if ($url->isPasswordProtected()) {
            return $this->handlePasswordProtectedUrl($request, $url);
        }

        // Track the click
        $this->analytics->trackClick($url, $request);

        // Redirect to original URL
        return redirect($url->original_url, 301);
    }

    /**
     * Handle password protected URLs.
     */
    private function handlePasswordProtectedUrl(Request $request, Url $url): RedirectResponse|View
    {
        // Check if password is provided in session
        if ($request->session()->has("url_password_{$url->id}")) {
            // Track the click
            $this->analytics->trackClick($url, $request);

            // Redirect to original URL
            return redirect($url->original_url, 301);
        }

        // Check if password is provided in request
        if ($request->has('password')) {
            if (Hash::check($request->input('password'), $url->password)) {
                // Store password verification in session
                $request->session()->put("url_password_{$url->id}", true);

                // Track the click
                $this->analytics->trackClick($url, $request);

                // Redirect to original URL
                return redirect($url->original_url, 301);
            } else {
                return view('urls.password', compact('url'))
                    ->withErrors(['password' => 'Invalid password.']);
            }
        }

        // Show password form
        return view('urls.password', compact('url'));
    }

    /**
     * Show URL preview (optional feature).
     */
    public function preview(string $code): View
    {
        $url = Url::where('short_code', $code)
            ->orWhere('custom_alias', $code)
            ->where('is_active', true)
            ->firstOrFail();

        return view('urls.preview', compact('url'));
    }

    /**
     * Generate QR code for URL.
     */
    public function qrCode(string $code): \Illuminate\Http\Response
    {
        $url = Url::where('short_code', $code)
            ->orWhere('custom_alias', $code)
            ->where('is_active', true)
            ->firstOrFail();

        if ($url->qr_code_path && \Storage::disk('public')->exists($url->qr_code_path)) {
            $qrCode = \Storage::disk('public')->get($url->qr_code_path);

            return response($qrCode, 200)
                ->header('Content-Type', 'image/png')
                ->header('Content-Disposition', 'inline; filename="qr-code.png"');
        }

        abort(404, 'QR code not found.');
    }
}
