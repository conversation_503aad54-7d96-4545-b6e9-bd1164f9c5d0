{"hash": "380861b0aa0ce9d3d1184f2e62efcf726ccefdb9", "user_agents": [{"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.2.1; en-us; A100 Build/HTK55D) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1; en-us; A110 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; de-de; A200 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.0.3", "Webkit": "534.30", "Safari": "4.0", "Build": "IML74K"}, "model": "A200"}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; A500 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; A501 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; A701 Build/JRO03H) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; B1-A71 Build/JZO54K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; B1-710 Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.72 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; A1-810 Build/JDQ39) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Safari/537.31", "mobile": true, "tablet": true}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; nl-nl; A1-810 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Acer; Allegro)", "mobile": true, "tablet": false}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; A3-A10 Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Safari/537.36", "mobile": true, "tablet": true, "version": {"Android": "4.2.2", "Build": "JDQ39", "Webkit": "537.36", "Chrome": "32.0.1700.99"}}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; A1-811 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; A1-830 Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; A3-A11 Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Acer", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; A3-A40 Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.91 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.7; en-in; MB525 Build/GWK74; CyanogenMod-7.2.0) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.5; it-it; ALCATEL ONE TOUCH 918D Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false, "version": {"Android": "2.3.5", "Webkit": "533.1", "Safari": "4.0", "Build": "GRJ90"}, "model": "ONE TOUCH 918D"}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; ru-ru; ALCATEL ONE TOUCH 991 Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false, "version": {"Android": "2.3.6", "Webkit": "533.1", "Safari": "4.0", "Build": "GRJ90"}, "model": "ONE TOUCH 991"}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; ALCATEL ONE TOUCH 993D Build/ICECREAM) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false, "version": {"Android": "4.0.4", "Webkit": "534.30", "Safari": "4.0", "Build": "ICECREAM"}, "model": "ONE TOUCH 993D"}, {"vendor": "Alcatel", "user_agent": "ALCATEL_A392G/1.0 ObigoInternetBrowser/Q05A[TF013513002719521000000013182904148]", "mobile": true, "tablet": false, "model": "A392G"}, {"vendor": "Alcatel", "user_agent": "ALCATEL_3020D/1.0 ObigoInternetBrowser/Q03C", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2; ALCATEL ONE TOUCH 5037A Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "ALCATEL_3020G/1.0 ObigoInternetBrowser/Q03C", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "ALCATEL_3041D/1.0 ObigoInternetBrowser/Q03C", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; ALCATEL ONE TOUCH 5037E Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ALCATEL ONE TOUCH 5037X Build/JDQ39) AppleWebKit/537.36 (KHTML like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; ALCATEL ONE TOUCH 5037X Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ONE TOUCH 6012A Build/JDQ39) AppleWebKit/537.36 (KHTML like Gecko) Chrome/28.0.1500.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-nz; ONE TOUCH 6012A Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ONE TOUCH 6012D Build/JDQ39) AppleWebKit/537.36 (KHTML like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; ONE TOUCH 6012D Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; En-us; ONE TOUCH 6012E Build/JDQ39) AppleWebKit/534.30 (KHT<PERSON>, Like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ONE TOUCH 6012X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.12975 YaBrowser/13.12.1599.12975 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; bg-bg; ONE TOUCH 6012X Build/JDQ39) AppleWebKit/534.30 (KHTML like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ALCATEL ONE TOUCH 6012X_orange Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; fr-fr; ALCATEL ONE TOUCH 6012X_orange Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; fr-fr; 6016E Build/JLS36C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; en-us; ALCATEL ONE TOUCH 6016E Build/JLS36C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; 6016X Build/JLS36C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; ALCATEL ONE TOUCH 6016X Build/JLS36C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.122 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; ru-ru; 6016X Build/JLS36C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ALCATEL ONE TOUCH 6032A Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.170 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; ru-ru; ALCATEL ONE TOUCH 6032X Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-nz; ALCATEL ONE TOUCH 7040A Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; th-th; ALCATEL ONE TOUCH 7040D Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ALCATEL ONE TOUCH 7040D Build/JDQ39) AppleWebKit/537.36 (KHTML like Gecko) Chrome/28.0.1500.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ALCATEL ONE TOUCH 7040E Build/JDQ39) AppleWebKit/537.36 (KHTML like Gecko) Chrome/28.0.1500.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; it-it; ALCATEL ONE TOUCH 7041D Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ALCATEL ONE TOUCH 7041D Build/JDQ39) AppleWebKit/537.36 (KHTML like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ALCATEL ONE TOUCH 7041X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.138 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; pt-pt; ALCATEL ONE TOUCH 7041X Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ALCATEL ONE TOUCH 8020A Build/JDQ39) AppleWebKit/537.36 (KHT<PERSON>, Like Gecko) Chrome/30.0.1599.92 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; ALCATEL ONE TOUCH 8020A Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ALCATEL ONE TOUCH 8020D Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; es-es; ALCATEL ONE TOUCH 8020D Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ALCATEL ONE TOUCH 8020E Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; ALCATEL ONE TOUCH 8020E Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ALCATEL ONE TOUCH 8020X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.136 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Alcatel", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; ALCATEL ONE TOUCH 8020X Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Allview", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; ALLVIEW P5 Build/IML74K) AppleWebKit/534.30 (KHTML like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Allview", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us ; ALLVIEW SPEEDI Build/IMM76D) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1/UCBrowser/8.5.3.246/145/355", "mobile": true, "tablet": true}, {"vendor": "Allview", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-us; AllviewCity Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Allview", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; ALLVIEWSPEED Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; KFTT Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Silk/3.4 Mobile Safari/535.19 Silk-Accelerated =true", "mobile": true, "tablet": true}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; U; en-US) AppleWebKit/528.5+ (KHTML, like Gecko, Safari/528.5+) Version/4.0 Kindle/3.0 (screen 600x800; rotate)", "mobile": true, "tablet": true, "version": {"Webkit": "528.5+", "Kindle": "3.0", "Safari": "4.0"}, "model": "Kindle"}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; KFOTE Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.0.3", "Build": "IML74K", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; WFJWAE Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; U; en-us; KFTT Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Silk/3.21 Safari/535.19 Silk-Accelerated=true", "mobile": true, "tablet": true}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; U; en-us; KFTHWI Build/JDQ39) AppleWebKit/535.19 (KHTML, like Gecko) Silk/3.21 Safari/535.19 Silk-Accelerated=true", "mobile": true, "tablet": true}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; U; en-us; KFJWI Build/IMM76D) AppleWebKit/535.19 (KHTML, like Gecko) Silk/3.21 Safari/535.19 Silk-Accelerated=true", "mobile": true, "tablet": true}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; U; en-us; KFSOWI Build/JDQ39) AppleWebKit/535.19 (KHTML, like Gecko) Silk/3.21 Safari/535.19 Silk-Accelerated=true", "mobile": true, "tablet": true}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.1; xx-xx; T720-WIFI Build/ECLAIR) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17", "mobile": true, "tablet": true}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; Android 5.1; KFARWI Build/LMY47O) AppleWebKit/537.36 (KHTML, like Gecko) Silk/47.1.79 like Chrome/47.0.2526.80 Mobile Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; KFTHWI Build/KTU84M) AppleWebKit/537.36 (KHTML, like Gecko) Silk/44.1.54 like Chrome/44.0.2403.63 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Silk/44.1.54 like Chrome/44.0.2403.63 Safari/537.36", "mobile": false, "tablet": false}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; KFASWI Build/LMY47O) AppleWebKit/537.36 (KHTML, like Gecko) Silk/48.2.2 like Chrome/48.0.2564.95 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; KFFOWI Build/LMY47O) AppleWebKit/537.36 (KHTML, like Gecko) Silk/46.1.66 like Chrome/46.0.2490.80 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; U; en-us; KFAPWI Build/JDQ39) AppleWebKit/535.19 (KHTML, like Gecko) Silk/3.13 Safari/535.19 Silk-Accelerated=true", "mobile": true, "tablet": true}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; KFGIWI Build/LVY48F) AppleWebKit/537.36 (KHTML, like Gecko) Silk/56.2.4 like Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Amazon", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.4.4; en-us; SD4930UR Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Silk/3.60 like Chrome/37.0.2026.117 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "AOC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; MW0922 Build/IMM76D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.111 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "AOC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; es-us; MW0831Plus Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Apple", "user_agent": "iTunes/9.1.1", "mobile": false, "tablet": false}, {"vendor": "Apple", "user_agent": "iTunes/11.0.2 (Windows; Microsoft Windows 8 x64 Business Edition (Build 9200)) AppleWebKit/536.27.1", "mobile": false, "tablet": false}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPod touch; CPU iPhone OS 7_0 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Version/7.0 Mobile/11A4449d Safari/9537.53", "mobile": true, "tablet": false}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPhone; U; CPU like Mac OS X; en) AppleWebKit/420+ (KHTML, like Gecko) Version/3.0 Mobile/1A543 Safari/419.3", "mobile": true, "tablet": false, "version": {"Webkit": "420+", "Safari": "3.0"}}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPhone; U; CPU iPhone OS 3_0 like Mac OS X; en-us) AppleWebKit/528.18 (KHTML, like Gecko) Version/4.0 Mobile/7A341 Safari/528.16", "mobile": true, "tablet": false, "version": {"iOS": "3_0", "Webkit": "528.18", "Safari": "4.0"}, "model": "iPhone"}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 5_1_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9B206 Safari/7534.48.3", "mobile": true, "tablet": false, "version": {"iOS": "5_1_1", "Webkit": "534.46", "Mobile": "9B206", "Safari": "5.1"}, "model": "iPhone"}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPod; CPU iPhone OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A403 Safari/8536.25", "mobile": true, "tablet": false, "version": {"iOS": "6_0", "Webkit": "536.26", "Mobile": "10A403", "Safari": "6.0"}, "model": "iPod"}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPad; CPU OS 5_1_1 like Mac OS X; en-us) AppleWebKit/534.46.0 (KHTML, like Gecko) CriOS/21.0.1180.80 Mobile/9B206 Safari/7534.48.3 (6FF046A0-1BC4-4E7D-8A9D-6BF17622A123)", "mobile": true, "tablet": true, "version": {"iOS": "5_1_1", "Webkit": "534.46.0", "Chrome": "21.0.1180.80", "Mobile": "9B206"}, "model": "iPad"}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPad; CPU OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A403 Safari/8536.25", "mobile": true, "tablet": true, "version": {"iOS": "6_0", "Webkit": "536.26", "Safari": "6.0", "Mobile": "10A403"}, "model": "iPad"}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPad; U; CPU OS 4_2_1 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8C148 Safari/6533.18.5", "mobile": true, "tablet": true, "version": {"iOS": "4_2_1", "Webkit": "533.17.9", "Safari": "5.0.2", "Mobile": "8C148"}, "model": "iPad"}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPad; U; CPU OS 3_2 like Mac OS X; en-us) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7B334b Safari/531.21.10", "mobile": true, "tablet": true, "version": {"iOS": "3_2", "Webkit": "531.21.10", "Safari": "4.0.4", "Mobile": "7B334b"}, "model": "iPad"}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 6_0_1 like Mac OS X; da-dk) AppleWebKit/534.46.0 (KHTML, like Gecko) CriOS/21.0.1180.82 Mobile/10A523 Safari/7534.48.3", "mobile": true, "tablet": false, "version": {"iOS": "6_0_1", "Webkit": "534.46.0", "Chrome": "21.0.1180.82", "Mobile": "10A523"}, "model": "iPhone"}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A523 Safari/8536.25", "mobile": true, "tablet": false, "version": {"iOS": "6_0_1", "Webkit": "536.26", "Safari": "6.0", "Mobile": "10A523"}, "model": "iPhone"}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 6_1 like Mac OS X; ru-ru) AppleWebKit/536.26 (KHTML, like Gecko) CriOS/23.0.1271.100 Mobile/10B142 Safari/8536.25", "mobile": true, "tablet": false, "version": {"iOS": "6_1", "Webkit": "536.26", "Chrome": "23.0.1271.100", "Mobile": "10B142"}, "model": "iPhone"}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B329 Safari/8536.25", "mobile": true, "tablet": false, "version": {"iOS": "6_1_3", "Webkit": "536.26", "Safari": "6.0", "Mobile": "10B329"}, "model": "iPhone"}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPad; CPU OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Coast/1.0.2.62956 Mobile/10B329 Safari/7534.48.3", "mobile": true, "tablet": true, "version": {"Coast": "1.0.2.62956"}}, {"vendor": "Apple", "user_agent": "CheckMyBus iOS mobile App 0.9.0 (iPhone; iPhone OS/7.1.1)", "mobile": true, "tablet": false}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPad; CPU OS 7_0 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Version/7.0 Mobile/11A465 Safari/9537.53", "mobile": true, "tablet": true}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_0_2 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) CriOS/38.0.2125.59 Mobile/12A405 Safari/600.1.4", "mobile": true, "tablet": false}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPad; CPU OS 13_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/92.0.4515.90 Mobile/15E148 Safari/604.1", "mobile": true, "tablet": true}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_5) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/92 Version/11.1.1 Safari/605.1.15", "mobile": false, "tablet": false}, {"vendor": "Apple", "user_agent": "VendorAppName/1.7.0 (iPhone; iOS 8.1.2; Scale/3.00)", "mobile": true, "tablet": false, "version": {"iOS": "8.1.2"}, "model": "iPhone"}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPad; CPU OS 9_0 like Mac OS X) AppleWebKit/601.1.17 (KHTML, like Gecko) Version/8.0 Mobile/13A175 Safari/600.1.4", "mobile": true, "tablet": true}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 9_0 like Mac OS X) AppleWebKit/601.1.37 (KHTML, like Gecko) Version/8.0 Mobile/13A4293g Safari/600.1.4", "mobile": true, "tablet": false}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPad; CPU OS 9_0_2 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13A452 Safari/601.1", "mobile": true, "tablet": true}, {"vendor": "Apple", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E302 rabbit%2F1.0 baiduboxapp/0_0.0.3.7_enohpi_4331_057/1.3.11_2C2%257enohPi/1099a/4D6A2107AC77E4AD3E534E146047A21C0EC9262D6OCCBFMJOPA/1", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Archos 97c Platinum Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.98 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Qilive 97R Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.92 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; Archos 50 Platinum Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; ARCHOS 80G9 Build/IMM76D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2.1; fr-fr; A101IT Build/FROYO) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Archos 101 Neon Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Archos 101 Cobalt Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; de-de; ARCHOS 80 TITANIUM Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; ARCHOS 101 Titanium Build/JRO03H) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; de-de; ARCHOS 70b TITANIUM Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; de-de; Archos 80 Xenon Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Archos 79 Xenon Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; de-de; ARCHOS 101 Titanium Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; de-de; ARCHOS 80XSK Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; de-de; ARCHOS FAMILYPAD 2 Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; ARCHOS 97B TITANIUM Build/JRO03H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ARCHOS 101 XS 2 Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; ARCHOS 80b PLATINUM Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Archos 70 Xenon Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; de-de; ARCHOS 97 CARBON Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; de-de; ARCHOS 97 TITANIUMHD Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Archos 90 Neon Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 1.6; de-de; Archos5 Build/Donut) AppleWebKit/528.5+ (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; de-de; ARCHOS GAMEPAD Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.2.1; en-us; Transformer TF101 Build/HTK75) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true, "version": {"Android": "3.2.1", "Webkit": "534.13", "Safari": "4.0"}, "model": "Transformer TF101"}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; Transformer Build/JRO03L) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; ASUS Transformer Pad TF300T Build/JRO03C) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; fr-fr; Transformer Build/JZO54K; CyanogenMod-10) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; asus_laptop Build/IMM76L) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-gb; PadFone 2 Build/JRO03L) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-gb; PadFone 2 Build/JRO03L) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.1.1", "Build": "JRO03L", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.1; ME301T Build/JOP40D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Safari/537.36", "mobile": true, "tablet": true, "version": {"Android": "4.2.1", "Build": "JOP40D"}}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.1; ME173X Build/JOP40D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Safari/537.36", "mobile": true, "tablet": true, "version": {"Android": "4.2.1", "Build": "JOP40D"}}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; TF300T Build/JDQ39E) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19", "mobile": true, "tablet": true, "version": {"Android": "4.2.2", "Build": "JDQ39E"}}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; K00C Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; K00E Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; K00F Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; K00L Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.131 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ME302KL Build/JDQ39) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Safari/537.31", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; K010 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.111 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; K017 Build/KVT49L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.111 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 5.0; PO1MA build/LRX21V) AppelWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.93 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0(<PERSON><PERSON>; Android 4.4.2; K011 Build/KOT49H)AppleWebKit/537.36 (KHTML, like Gecko)Chrome/53.0.2785.124 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; K01E Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.93 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; P01Z Build/LRX22G; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; P027 Build/MRA58L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; P024 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 YaBrowser/17.10.2.145.01 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; P024 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; P00C Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; ASUS_Z01QD) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.185 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; K01A Build/JSS15Q) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.111 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "ASUS", "user_agent": "Mozilla/5.0 (Linux; Android 9; ASUS_X00TD; Flow) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/359.0.0.288 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (BlackB<PERSON>; U; BlackBerry 9300; en) AppleWebKit/534.8+ (KHTML, like Gecko) Version/6.0.0.546 Mobile Safari/534.8+", "mobile": true, "tablet": false, "version": {"Webkit": "534.8+", "BlackBerry": "6.0.0.546"}, "model": "BlackBerry 9300"}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Black<PERSON><PERSON>; <PERSON>; BlackBerry 9360; en-US) AppleWebKit/534.11+ (KHTML, like Gecko) Version/7.0.0.400 Mobile Safari/534.11+", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (<PERSON><PERSON><PERSON>; <PERSON>; BlackBerry 9700; he) AppleWebKit/534.8+ (KHTML, like Gecko) Version/6.0.0.723 Mobile Safari/534.8+", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (BlackB<PERSON>; U; BlackBerry 9700; en-US) AppleWebKit/534.8  (KHTML, like Gecko) Version/6.0.0.448 Mobile Safari/534.8", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (BlackB<PERSON>; <PERSON>; BlackBerry 9790; en-GB) AppleWebKit/534.11+ (KHTML, like Gecko) Version/7.1.0.714 Mobile Safari/534.11+", "mobile": true, "tablet": false, "version": {"Webkit": "534.11+", "BlackBerry": "7.1.0.714"}, "model": "BlackBerry 9790"}, {"vendor": "BlackBerry", "user_agent": "Opera/9.80 (<PERSON><PERSON><PERSON>; Opera Mini/7.0.29990/28.2504; U; en) Presto/2.8.119 Version/11.10", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (BlackB<PERSON>; U; BlackBerry 9981; en-GB) AppleWebKit/534.11+ (KHTML, like Gecko) Version/7.1.0.342 Mobile Safari/534.11+", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (BlackB<PERSON>; U; BlackBerry 9800; en-GB) AppleWebKit/534.8+ (KHTML, like Gecko) Version/6.0.0.546 Mobile Safari/534.8+", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (BlackB<PERSON>; U; BlackBerry 9780; es) AppleWebKit/534.8  (KHTML, like Gecko) Version/6.0.0.480 Mobile Safari/534.8", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (BlackB<PERSON>; U; BlackBerry 9810; en-US) AppleWebKit/534.11  (KHTML, like Gecko) Version/7.0.0.583 Mobile Safari/534.11", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Black<PERSON><PERSON>; <PERSON>; BlackBerry 9860; es) AppleWebKit/534.11+ (KHTML, like Gecko) Version/7.0.0.576 Mobile Safari/534.11+", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (BlackB<PERSON>; U; BlackBerry 9900; en-US) AppleWebKit/534.11  (KHTML, like Gecko) Version/7.1.0.523 Mobile Safari/534.11", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "BlackBerry8520/5.0.0.592 Profile/MIDP-2.1 Configuration/CLDC-1.1 VendorID/136", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "BlackBerry8520/5.0.0.1067 Profile/MIDP-2.1 Configuration/CLDC-1.1 VendorID/603", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "BlackBerry8520/5.0.0.1036 Profile/MIDP-2.1 Configuration/CLDC-1.1 VendorID/611", "mobile": true, "tablet": false, "version": {"BlackBerry": "5.0.0.1036", "VendorID": "611"}, "model": "BlackBerry8520"}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (BlackB<PERSON>; <PERSON>; BlackBerry 9220; en) AppleWebKit/534.11+ (KHTML, like Gecko) Version/7.1.0.337 Mobile Safari/534.11+", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (<PERSON>B<PERSON>; U; RIM Tablet OS 2.1.0; en-US) AppleWebKit/536.2+ (KHTML, like Gecko) Version/7.2.1.0 Safari/536.2+", "mobile": true, "tablet": true}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (BB10; Touch) AppleWebKit/537.1+ (KHTML, like Gecko) Version/10.0.0.1337 Mobile Safari/537.1+", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (BB10; Touch) AppleWebKit/537.10+ (KHTML, like Gecko) Version/10.0.9.2372 Mobile Safari/537.10+", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (BB10; Touch) /537.10+ (KHTML, like Gecko) Version/10.0.9.2372 Mobile Safari/537.10+", "mobile": true, "tablet": false, "version": {"BlackBerry": "10.0.9.2372"}}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.2.1; en-us; Transformer TF101 Build/HTK75) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true, "version": {"Android": "3.2.1", "Webkit": "534.13", "Safari": "4.0"}, "model": "Transformer TF101"}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; de-de; A200 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.0.3", "Webkit": "534.30", "Safari": "4.0"}, "model": "A200"}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; A500 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.0.3", "Webkit": "534.30", "Safari": "4.0"}, "model": "A500"}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; A501 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.0.3", "Webkit": "534.30", "Safari": "4.0"}, "model": "A501"}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; Transformer Build/JRO03L) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Safari/535.19", "mobile": true, "tablet": true, "version": {"Android": "4.1.1", "Webkit": "535.19", "Chrome": "18.0.1025.166"}, "model": "Transformer"}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; ASUS Transformer Pad TF300T Build/JRO03C) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Safari/535.19", "mobile": true, "tablet": true, "version": {"Android": "4.1.1", "Webkit": "535.19", "Chrome": "18.0.1025.166"}, "model": "Transformer Pad TF300T"}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; fr-fr; Transformer Build/JZO54K; CyanogenMod-10) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.1.2", "Webkit": "534.30", "Safari": "4.0", "Build": "JZO54K"}, "model": "Transformer"}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; B1-A71 Build/JZO54K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19", "mobile": true, "tablet": true, "version": {"Android": "4.1.2", "Webkit": "535.19", "Chrome": "18.0.1025.166"}, "model": "B1-A71"}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Acer; Allegro)", "mobile": true, "tablet": false, "version": {"Windows Phone OS": "7.5", "Trident": "5.0", "IE": "9.0"}, "model": "Allegro"}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; BBA100-1 Build/6.0.1_0.223.0.064) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; BBB100-1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.89 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; BBB100-3 Build/NMF26F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; Android 7.1; BBD100-1 Build/N2G47H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.84 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; BBE100-1 Build/OPM1.171019.026) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; BBF100-9 Build/OPM1.171019.026) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "BlackBerry", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; STH100-1 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.91 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Dell", "user_agent": "Mozilla/5.0 (Linux; U; Android 1.6; en-gb; Dell Streak Build/Donut AppleWebKit/528.5+ (KHTML, like Gecko) Version/3.1.2 Mobile Safari/ 525.20.1", "mobile": true, "tablet": false}, {"vendor": "Dell", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.7; hd-us; Dell Venue Build/GWK74; CyanogenMod-7.2.0) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Dell", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; DELL; Venue Pro)", "mobile": true, "tablet": false}, {"vendor": "Dell", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; Venue 8 3830 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Dell", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; Venue 7 3730 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Dell", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; Venue 7 HSPA+ Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.117 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Dell", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; en-us; Venue 8 3830 Build/JSS15Q) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Dell", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.2; zh-cn; Dell Streak 10 Pro Build/HTJ85B) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true}, {"vendor": "Dell", "user_agent": "Mozilla/5.0 (Linux; U; Android 7.0.1; en-US; Dell Streak 7 Build/FRF91) AppleWebKit/51.0 (KHTML, like Gecko) Version/4.0 Mobile Safari/51.0", "mobile": true, "tablet": true}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2; en-us; Nexus One Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Nexus 4 Build/JDQ39) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.40 Mobile Safari/537.31 OPR/14.0.1074.54070", "mobile": true, "tablet": false, "version": {"Android": "4.2.2", "Build": "JDQ39", "Webkit": "537.31", "Opera": "14.0.1074.54070"}}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Nexus 4 Build/JDQ39) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31", "mobile": true, "tablet": false, "version": {"Android": "4.2.2", "Chrome": "26.0.1410.58"}}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-us; Google Nexus 4 - 4.1.1 - API 16 - 768x1280 Build/JRO03S) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-us; Google Galaxy Nexus - 4.1.1 - API 16 - 720x1280 Build/JRO03S) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; Nexus 7 Build/JRO03D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; Android 4.2; Nexus 7 Build/JOP40C) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; Nexus 7 Build/JZ054K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Safari/535.19", "mobile": true, "tablet": true, "version": {"Android": "4.1.2", "Chrome": "18.0.1025.166"}}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; cs-cz; Nexus S Build/JZO54K; CyanogenMod-10.0.0) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; Nexus 10 Build/JWR66Y) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.72 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; U; Android; en_us; Nexus 7 Build/) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 NetFrontLifeBrowser/2.3 Mobile (Dragonfruit)", "mobile": true, "tablet": true}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; Android 5.1; Nexus 5 Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/39.0.0.0 Mobile Safari/537.36 momoWebView/6.3.1 android/404(Nexus 5;android 5.1;zh_CN;10;netType/1)", "mobile": true, "tablet": false}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; Nexus 10 Build/JWR66Y) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.92 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; Android 7.1; Pixel XL Build/NDE63H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.85 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; Android 7.1; Pixel Build/NDE63H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.85 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/65.0.3325.109 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM1.171019.026) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.126 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Google", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel 2 Build/OPM2.171026.006.G1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HP", "user_agent": "Mozilla/5.0 (hp-tablet; Linux; hpwOS/3.0.5; U; en-GB) AppleWebKit/534.6 (KHTML, like Gecko) wOSBrowser/234.83 Safari/534.6 TouchPad/1.0", "mobile": true, "tablet": true}, {"vendor": "HP", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-gb; HP Slate 7 Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "HP", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HP Slate 7 Build/JRO03H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "HP", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; HP 8 Build/1.0.7_WW-FIR-13) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "HP", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HP Slate 10 HD Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "HP", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HP Slate 8 Pro Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "HP", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Slate 21 Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.138 Safari/537.36 OPR/22.0.1485.78487", "mobile": true, "tablet": true}, {"vendor": "HP", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HP SlateBook 10 x2 PC Build/4.3-17r20-03-23) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (X11; Linux x86_64; Z520m; en-ca) AppleWebKit/534.24 (KHTML, like Gecko) Chrome/11.0.696.34 Safari/534.24", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "HTC_Touch_HD_T8282 Mozilla/4.0 (compatible; MSIE 6.0; Windows CE; IEMobile 7.11)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 1.5; en-us; ADR6200 Build/CUPCAKE) AppleWebKit/528.5+ (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.1; xx-xx; Desire_A8181 Build/ERE27) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.1-update1; de-de; HTC Desire 1.19.161.5 Build/ERE27) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.1-update1; en-gb; HTC Desire Build/ERE27) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2; fr-fr; HTC Desire Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2; en-dk; Desire_A8181 Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2; xx-xx; 001HT Build/FRF91) AppleWebKit/525.10+ (KHTML, like Gecko) Version/3.0.4 Mobile Safari/523.12.2", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2; xx-xx; HTCA8180/1.0 Android/2.2 release/06.23.2010 Browser/WAP 2.0 Profile/MIDP-2.0 Configuration/CLDC-1.1 Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2.2; de-at; HTC Desire Build/FRG83G) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2.2; en-sk; Desire_A8181 Build/FRG83G) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3; xx-xx; HTC/DesireS/1.07.163.1 Build/GRH78C) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.3; en-lv; HTC_DesireZ_A7272 Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.4; en-us; ADR6300 Build/GRJ22) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.5; en-gb; HTC/DesireS/2.10.161.3 Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.5; ru-ru; HTC_DesireS_S510e Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.5; en-us; Inspire 4G Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.5; de-de; HTC Explorer A310e Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.5; en-gb; HTC_ChaCha_A810e Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.5; nl-nl; HTC_DesireHD_A9191 Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.5; ru-ru; HTC Desire S Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.3; en-au; HTC Desire Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.5; de-de; HTC_DesireHD Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.5; ru-ua; HTC_WildfireS_A510e Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.7; en-us; HTC Vision Build/GRI40; ILWT-CM7) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0; xx-xx; HTC_GOF_U/1.05.161.1 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; hu-hu; HTC Sensation Z710e Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; zh-cn; HTC Sensation XE with Beats Audio Z715e Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 XiaoMi/MiuiBrowser/1.0", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; pl-pl; EVO3D_X515m Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; ru-ru; HTC_One_S Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; ru-ru; HTC_One_V Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; zh-cn; HTC_A320e Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; zh-tw; HTC Desire V Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; PG86100 Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-nl; SensationXE_Beats_Z715e Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; ADR6425LVW 4G Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One V Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; cs-ee; Sensation_Z710e Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; HTC Evo 4G Build/MIUI) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; Desire HD Build/IMM76D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-my; HTC_One_X Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; it-it; IncredibleS_S710e Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; fr-fr; HTC_Desire_S Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC Butterfly Build/JRO03C) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; EVO Build/JRO03C) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Mobile Safari/537.22", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; HTCSensation Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; en-us; GT-S6312 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC 7 Mozart T8698; QSD8x50)", "mobile": true, "tablet": false, "version": {"IE": "9.0", "Windows Phone OS": "7.5", "Trident": "5.0"}, "model": "7 Mozart T8698"}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.2.1; en-gb;HTC_Flyer_P512 Build/HTK75C) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.1; zh-tw; HTC PG09410 Build/HMJ15) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; 7 HTC MOZART)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; 7 Mondrian T8788)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; 7 Mozart T8698)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; 7 Mozart)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; 7 <PERSON>; Orange)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; 7 Pro T7576)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; 7 Pro)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; 7 Schubert T9292)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; 7 Surround)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; 7 Trophy T8686)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; 7 Trophy)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Eternity)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Gold)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; HD2 LEO)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; HD2)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; HD7 T9292)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; HD7)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; iPad 3)", "mobile": true, "tablet": true}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; LEO)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Mazaa)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Mondrian)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Mozart T8698)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; <PERSON><PERSON>; <PERSON>)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; mwp6985)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; PC40100)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; PC40200)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; PD67100)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; PI39100)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; PI86100)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Radar 4G)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Radar C110e)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Radar C110e; 1.08.164.02)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Radar C110e; 2.05.164.01)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Radar C110e; 2.05.168.02)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Radar)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Radar; Orange)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Schuber)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Schubert T9292)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; <PERSON><PERSON>; <PERSON>)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Spark)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Surround)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; T7575)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; T8697)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; T8788)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; T9295)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; T9296)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; TITAN X310e)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Titan)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; Torphy T8686)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; X310e)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC_blocked; T8788)", "mobile": true, "tablet": false, "version": {"IE": "9.0", "Windows Phone OS": "7.5", "Trident": "5.0"}, "model": "T8788"}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; HTC One S Build/IMM76D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; de-de; HTC One X Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-th; HTC One V Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; HTC One X Build/IMM76D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-id; HTC One X Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One S Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One X Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; de-at; HTC One S Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03L) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-gb; HTC One S Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.123 Mobile Safari/537.22", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.123 Mobile Safari/537.22", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Mobile Safari/537.22", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; HTC One X Build/JZO54K) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Mobile Safari/537.22", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; es-pe; HTC One V Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Mobile Safari/537.22", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One V Build/IML74K) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; de-de; HTC One X Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 Maxthon/4.0.4.1000", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One X Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.133 Mobile Safari/535.19 AlexaToolbar/alxf-2.17", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; HTC One Build/JZO54K) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One S Build/IML74K) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.90 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.90 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; HTC One Build/JZO54K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; HTC One Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.90 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; de-de; HTC One Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31 OPR/14.0.1074.58201", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; es-es; HTC One S Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; HTC One Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.64 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.64 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One V Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.64 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39E) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One XL Build/JRO03C) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.63 Mobile Safari/537.36 OPR/15.0.1162.60140", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; de-de; HTC One S Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; HTC One Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One X Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; HTC One Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.24 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "HTC One X Linux/3.0.13 Android/4.1.9 Release/10.12.2012 Browser/AppleWebKit534.30 Profile/MIDP-2.0 Configuration/CLDC-1.1 Mobile Safari/534.30 Android 4.0.1;", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; zh-tw; HTC One 801e Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; HTC One X Build/IMM76D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.63 Mobile Safari/537.36 OPR/15.0.1162.61541", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.72 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One XL Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; HTC One Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.72 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.72 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One X+ Build/JRO03C) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; HTC One X Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30/4.05d.1002.m7", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-in; HTC One V Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.72 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One V Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.72 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; ru-ru; HTC One X Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 MicroMessenger/5.0.1.352", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-us; HTC One X Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-gb; HTC One Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.72 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; HTC One SV Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.82 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.82 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One V Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.82 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.82 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.82 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.92 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One mini Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.82 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; HTC One Build/IMM76D) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.82 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One 801e Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.72 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.92 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.92 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.92 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-ch; HTC One Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; nl-nl; HTC One X Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.92 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; zh-cn; HTC One S Build/IML74K) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17 T5/2.0 baidubrowser/3.1.6.4 (<PERSON><PERSON>; P1 4.0.3)", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One X+ Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; en-us; HTC One 801e Build/JSS15J) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 UCBrowser/9.4.1.362 U3/0.8.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4; HTC One Build/KRT16S.H5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; HTC One SV Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.1; ru-ru; HTC One Build/JOP40D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; ru-ru; HTC One 801e Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One 801e Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One mini Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.12975 YaBrowser/13.12.1599.12975 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4; HTC One Build/KRT16S) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; en-us; HTC One Build/JSS15J) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; HTC One X Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One Build/JWR66Y.H1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.93 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; de-at; HTC One Build/JSS15J) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.136 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.135 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X+ Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.136 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One dual sim Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.136 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; HTC One S Build/IMM76D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.136 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One max Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.132 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.136 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One mini Build/JSS15Q) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.136 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One 801e Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.136 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; zh-cn; HTC One Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 MicroMessenger/5.2.380", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.136 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.166 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One X Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.133 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.166 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.166 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X+ Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.166 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.132 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; HTC One X Build/IMM76D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; it-it; HTC One S Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-kw; HTC One X+ Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One max Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36 MicroMessenger/5.3.0.49_r693790.420", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.122 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.122 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.122 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; ru; HTC One V Build/IML74K) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 UCBrowser/9.7.5.418 U3/0.8.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.138 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36 MicroMessenger/5.2.1.381", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.122 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One mini Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.138 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.4.2; ru-ru; HTC One mini Build/KOT49H) AppleWebKit/537.16 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.16", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; ru; HTC One V Build/IML74K) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 UCBrowser/9.8.0.435 U3/0.8.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One 801e Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; HTC One X - 4.2.2 - API 17 - 720x1280 Build/JDQ39E) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One_M8 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One V Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.138 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X+ Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.128 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One_M8 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.128 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.128 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One_M8 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.131 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.131 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.131 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One_M8 Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.131 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; HTC One VX Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.131 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.76 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One_M8 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.135 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.135 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One V Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One dual sim Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.135 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One dual sim Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.138 Mobile Safari/537.36 OPR/22.0.1485.78487", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X+ Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.135 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.131 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One_M8 Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.135 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.135 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.117 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.135 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One 801e Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One 801e Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.117 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; HTC One X Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.138 Mobile Safari/537.36 OPR/22.0.1485.81203", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.117 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One 801e Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.117 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One mini Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.135 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; HTC One S Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.117 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; zh-tw; HTC One S Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One_M8 Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.117 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; HTC One V Build/IML74K) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 UCBrowser/9.9.2.467 U3/0.8.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.117 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One mini Build/JSS15Q) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.117 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.4.2; en-us; HTC One_M8 Build/KOT49H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.117 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One dual sim Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.117 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.72 Mobile Safari/537.36 OPR/19.0.1340.69721", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.102 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.102 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One_M8 Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.102 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One_M8 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.102 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One SV Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.102 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Mobile Safari/537.36 ACHEETAHI/2100050056", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; HTC One_M8 Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.102 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.102 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One_M8 Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One Build/JWR66Y.H1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; zh-tw; HTC One SV Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One mini Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; HTC One_M8 Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Mobile Safari/537.36 [FBAN/FB4A;FBAV/21.0.0.23.12;]", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; zh-tw; HTC One X Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; HTC One_M8 Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; ru-si; HTC One X Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 MicroMessenger/6.0.0.67_r853700.483 NetType/WIFI", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One_M8 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36 [FBAN/FB4A;FBAV/22.0.0.15.13;]", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-US; HTC One X Build/JRO03C) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 UCBrowser/10.0.1.512 U3/0.8.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; HTC One Build/KTU84P.H1) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Mobile Safari/537.36 [FBAN/FB4A;FBAV/22.0.0.15.13;]", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One mini Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36 [FBAN/FB4A;FBAV/22.0.0.15.13;]", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One SV Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; HTC One_M8 Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.93 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.93 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Mobile Safari/537.36 [FBAN/FB4A;FBAV/22.0.0.15.13;]", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One_M8 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; zh-tw; HTC One X+ Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; HTC One_M8 Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Mobile Safari/537.36 BingWeb/5.2.0.20140710", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; HTC One Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.93 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; es-mx; HTC One S Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 [FBAN/FB4A;FBAV/23.0.0.22.14;]", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One mini Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.4.3; es-es; HTC One 801e Build/KTU84L) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One mini Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.93 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X+ Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.93 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One_M8 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.2214.69 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; ru-ru; HTC One S Build/JRO03C) AppleWebKit/537.16 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.16", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/24.0.0.30.15;]", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.4.2; en-th; HTC One Build/KOT49H) AppleWebKit/537.16 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.16", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; es-ar; HTC One X Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 [FB_IAB/FB4A;FBAV/24.0.0.30.15;]", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One_M8 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/24.0.0.30.15;]", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.93 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; es-us; HTC One X+ Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 [FB_IAB/FB4A;FBAV/24.0.0.30.15;]", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; es-mx; HTC One S Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 [FB_IAB/FB4A;FBAV/24.0.0.30.15;]", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/24.0.0.30.15;]", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.136 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; HTC One Build/KTU84L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.2214.89 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One dual sim Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.2214.89 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/25.0.0.19.30;]", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.1; HTC One_M8 Build/LRX22C) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/37.0.0.0 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/25.0.0.19.30;]", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.2214.89 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; HTC One_M8 Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.2214.109 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; HTC One X Build/JDQ39) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; HTC One mini Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.2214.109 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.1; HTC One_M8 Build/LRX22C.H5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.2214.109 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel Build/OPR6.170623.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.125 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "HTC", "user_agent": "Mozilla/5.0 (Linux; Android 7.1; Pixel XL Build/NDE63H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 10; Pixel) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 9; Pixel XL Build/PQ3A.190801.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.149 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/263.0.0.46.121;]", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; Pixel XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.96 Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.2; PIXEL 2 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 10; Pixel 2 XL Build/QQ2A.200305.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.132 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/262.0.0.34.117;]", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 8.0.0; Pixel 2 XL Build/OPD1.170816.004) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.9805.620 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 10; Pixel 2 Build/QQ1A.191205.008; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.149 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/261.0.0.52.126;]", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; Pixel C Build/OPM8.190405.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.132 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 10; Pixel Build/QP1A.191005.007.A3; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.132 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/261.0.0.52.126;]", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 10; Pixel 3 Build/QQ2A.200305.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.149 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/263.0.0.46.121;]", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 10; Pixel 3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 10; Pixel 3a) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Mobile Safari/537.36 OPR/57.1.2830.52480", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 10; Pixel 3a XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 9; Pixel 3 XL Build/PQ3A.190801.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/76.0.3809.89 Mobile Safari/537.36 T7/11.21 SP-engine/2.17.0 baiduboxapp/11.21.0.10 (Baidu; P1 9)", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 10; Pixel 4 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Pixel", "user_agent": "Mozilla/5.0 (Linux; Android 11; Pixel 4a) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.1-update1; bg-bg; Ideos S7 Build/ERE27) AppleWebKit/525.10+ (KHTML, like Gecko) Version/3.0.4 Mobile Safari/523.12.2", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.1; en-us; Ideos S7 Build/ERE27) AppleWebKit/525.10+ (KHTML, like Gecko) Version/3.0.4 Mobile Safari/523.12.2", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; lt-lt; U8660 Build/HuaweiU8660) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.7; ru-ru; HUAWEI-U8850 Build/HuaweiU8850) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.2; pl-pl; MediaPad Build/HuaweiMediaPad) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.2; nl-nl; HUAWEI MediaPad Build/HuaweiMediaPad) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "HUAWEI_T8951_TD/1.0 Android/4.0.4 (Linux; U; Android 4.0.4; zh-cn) Release/05.31.2012 Browser/WAP2.0 (AppleWebKit/534.30) Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; ar-eg; MediaPad 7 Youth Build/HuaweiMediaPad) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; zh-cn; HW-HUAWEI_C8815/C8815V100R001C541B135; 540*960; CTC/2.0) AppleWebKit/534.30 (KHTML, like Gecko) Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; zh-cn; HW-HUAWEI_C8813D/C8813DV100R001C92B172; 480*854; CTC/2.0) AppleWebKit/534.30 (KHTML, like Gecko) Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; zh-cn; HW-HUAWEI_Y300C/Y300CV100R001C92B168; 480*800; CTC/2.0) AppleWebKit/534.30 (KHTML, like Gecko) Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; id-id; HUAWEI Y330-U11 Build/HuaweiY330-U11) AppleWebKit/537.16 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.16 Chrome/33.0.0.0", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; HUAWEI M2-A01L Build/HUAWEIM2-A01L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.125 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; HUAWEI GRA-L09 Build/HUAWEIGRA-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; FRD-L09 Build/HUAWEIFRD-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 8.0; DUK-AL20 Build/HUAWEIDUK-AL20; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.132 MQQBrowser/6.2 TBS/044054 Mobile Safari/537.36 V1_AND_SQ_7.5.8_818_YYB_D QQ/7.5.8.3490 NetType/WIFI WebP/0.3.0 Pixe", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; BAH-L09 Build/HUAWEIBAH-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.126 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; BAH-W09 Build/HUAWEIBAH-W09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.84 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; WAS-LX3 Build/HUAWEIWAS-LX3; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.111 Mobile Safari/537.36 [FB_IAB/Orca-Android;FBAV/162.0.0.19.90;]", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; Nexus 6P Build/OPM3.171019.014) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.137 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 8.0.0; CMR-AL19 Build/HUAWEICMR-AL19) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.70 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; AGS-L09 Build/HUAWEIAGS-L09; xx-xx) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/59.0.3071.125 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 8.0.0; EDI-AL10 Build/HUAWEIEDISON-AL10; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/72.0.3626.121 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; cs-cz; HUAWEI G510-0200 Build/HuaweiG510-0200) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 10; KOB2-L09) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.166 Safari/537.36 OPR/65.1.3381.61266", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; BG2-U03) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.101 Mobile Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; BG2-W09 Build/HuaweiBAGGIO2; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/55.0.2883.91 Mobile Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; en-us; Iris 349 Build/MocorDroid2.3.5) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; iris402+ Build/iris402+) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; IRIS402 Build/LAVAIRIS402) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; iris405 Build/LAVAIRIS405) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; IRIS_501 Build/LAVAIRIS501) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; iris402e Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; iris503e Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "UCWEB/2.0 (Linux; U; Opera Mini/7.1.32052/30.3697; en-US; IRIS402) U2/1.0.0 UCBrowser/9.1.1.420 Mobile", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "UCWEB/2.0 (MIDP-2.0; U; Adr 4.2.2; en-US; IRIS402) U2/1.0.0 UCBrowser/9.1.1.420 U2/1.0.0 Mobile", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "UCWEB/2.0 (Linux; U; Opera Mini/7.1.32052/30.3697; en-US; IRIS355) U2/1.0.0 UCBrowser/9.1.1.420 Mobile", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; iris356 Build/irisIRIS356) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 UCBrowser/9.5.0.360 U3/0.8.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "UCWEB/2.0 (Linux; U; Opera Mini/7.1.32052/30.3697; en-US; iris356) U2/1.0.0 UCBrowser/9.0.2.389 Mobile", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; iris500 Build/iris500) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; iris700 Build/iris700) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; QPAD E704 Build/JDQ39) AppleWebKit/537.36 (KHTML like Gecko) Chrome/36.0.1985.131 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2; xx-xx; IvoryS Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; en-US; E-TAB IVORY Build/E702) AppleWebKit/534.31 (KHTML, like Gecko) UCBrowser/9.3.0.321 U3/0.8.0 Mobile Safari/534.31", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.4; tr-tr; E-TAB Build/GRJ22) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": 0, "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-au; TBLT10Q-32GB Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; es-es; IdeaTab_A1107 Build/MR1) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; IdeaTab A2107A-H Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.90 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-au; ThinkPad Tablet Build/ThinkPadTablet_A400_03) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "UCWEB/2.0 (Linux; U; Opera Mini/7.1.32052/30.3697; en-US; IdeaTabA1000-G) U2/1.0.0 UCBrowser/9.2.0.419 Mobile", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; IdeaTabA1000-F Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.1; Lenovo A3000-H Build/JOP40D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.117 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; IdeaTab A3000-F Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Safari/537.360", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1; zh-cn; Lenovo-A3000-H/S100) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.1 Mobile Safari/534.300", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; es-us; IdeaTab A3000-F Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; IdeaTab A2107A-H Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; de-de; IdeaTab A2107A-H Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; es-es; IdeaTabA2109A Build/JRO03R) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; nl-nl; IdeaTabA2109A Build/JRO03R) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; es-es; IdeaTab_A1107 Build/MR1) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.300", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; IdeaTab S6000-H Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.138 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-gb; IdeaTab S6000-F Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Lenovo B8000-F Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2;it-it; Lenovo B8000-F/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2.2 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; it-it; Lenovo B6000-F/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.2.2 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Lenovo B6000-F Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; IdeaPadA10 Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.166 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; Ideapad K1 Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.7; de-de; IdeaPad A1 Build/GRK393; CyanogenMod-7) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; Lenovo B8080-H Build/JLS36C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; hu-hu; Lenovo A3500-FL Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; Lenovo A7600-F Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; Lenovo A5500-F Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.131 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Lenovo A390 Linux/3.0.13 Android/4.4.2 Release/04.03.2013 Browser/AppleWebKit534.30 Profile/MIDP-2.0 Configuration/CLDC-1.1 Mobile Safari/534.30 Android 4.0.1;", "mobile": true, "tablet": false}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; Lenovo TAB 2 A7-30F Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.2454.84 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; Lenovo A319 Build/MocorDroid4.4.2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.95 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 5.1; Lenovo YT3-X90L Build/LMY47I) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.105 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; Lenovo TB-X103F Build/LenovoTB-X103F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; Lenovo TB-X304F Build/NMF26F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.116 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; Lenovo TB-8703F Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; Lenovo P2a42 Build/NRD90N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.2987.132 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; Lenovo P2a42 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; Lenovo TB-X304L Build/NMF26F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; Tab2A7-10F Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36 ACHEETAHI/1", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; Lenovo TB2-X30L Build/LenovoTB2-X30L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.109 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0(Linux;Android 6.0.1;Lenovo YT3-X50L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.80 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; Lenovo TB2-X30F Build/LenovoTB2-X30F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.81 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; Lenovo TB2-X30F Build/LenovoTB2-X30F; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/43.0.2357.121 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; Lenovo TB2-X30M_PRC_YZ_A Build/LenovoTB2-X30M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2564.116 Safari/537.36 T7/10.4 light/1.0 baiduboxapp/10.4.5.11 (Baidu; P1 5.1.1)", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.1(Linux; U; Android 5.0; zh-cn; Lenovo YT3-X50M Build/LMY47V) AppleWebKit/537.36(KHTML, like Gecko) Version/4.0 Chrome/38.0.2125.102 Mobile Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; Lenovo YT3-X50F Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.154 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; Tab2A7-20F Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36 hsp", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Lenovo A5500-HV Build/JDQ39; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2564.116 Safari/537.36 T7/7.9 baiduboxapp/9.0.0.10 (Baidu; P1 4.2.2)", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 5.0; zh-cn; LNV-Lenovo A5500/A5500_S165_161206; 1280x720; CTC/2.0) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/37.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; Lenovo YT-X703F Build/S100) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; Lenovo YT-X703L Build/S100; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/58.0.3029.83 Safari/537.36 clicash_android v=1.671", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; Lenovo TB-8703N Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.154 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; Lenovo TB-8704N Build/NMF26F; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2564.116 Safari/537.36 T7/10.6 baiduboxapp/10.6.5.10 (Baidu; P1 7.1.1)", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; Lenovo TB-8704F Build/NMF26F; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2564.116 Safari/537.36 T7/10.5 baiduboxapp/10.5.5.10 (Baidu; P1 7.1.1)", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Lenovo TB3-730M Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/46.0.2490.76 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Lenovo TB3-730F Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2564.116 Safari/537.36 T7/10.7 baiduboxapp/10.7.5.10 (Baidu; P1 6.0)", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Lenovo TB3-730X Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.109 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; Lenovo TB-X704F Build/NMF26F; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/52.0.2743.100 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; Lenovo TB-X104F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Lenovo TB3-X70F Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2564.116 Safari/537.36 T7/10.3 baiduboxapp/10.3.6.13 (Baidu; P1 6.0)", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; U; Android 6.0; zh-C<PERSON>; Lenovo TB3-X70F Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.108 UCBrowser/11.9.4.974 UWS/2.13.2.100 Mobile Safari/537.36 AliApp(DingTalk/5.0.5) com.alibaba.android.rimet/1272", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 9; Lenovo TB-X705F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.81 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; Lenovo TB-8504F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.1; Lenovo TB3-710F Build/LRX21M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.76 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.1; Lenovo TB3-710F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.132 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; Lenovo TB-X704L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 11; Lenovo TB-J606F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.85 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 10; Lenovo TB-X606F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 10; Lenovo TB-X306X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.74 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Lenovo", "user_agent": "Mozilla/5.0 (Linux; Android 12; Lenovo YT-J706X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; en-us; LG-VS410PP Build/GRK39F) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2; en-us; LG-P509 Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 MMS/LG-Android-MMS-V1.0/1.2", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2.2; pt-br; LG-P350f Build/FRG83G) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 MMS/LG-Android-MMS-V1.0/1.2", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.3; en-us; LG-P500 Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 MMS/LG-Android-MMS-V1.0/1.2", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.3; en-us; LS670 Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.4; ru-ru; LG-E510 Build/GRJ22) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 MMS/LG-Android-MMS-V1.0/1.2", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.4; en-us; VS910 4G Build/GRJ22) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; nl-nl; LG-P700 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; ko-kr; LG-L160L Build/IML74K) AppleWebkit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; LG-F160S Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; nl-nl; LG-E610v/V10f Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; LG-E612 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; LG-F180K Build/JZO54K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; LG-V500 Build/JDQ39B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; LG-LW770 Build/IMM76I) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; LG-V510 Build/KOT49H.L004) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; LG; LG E-900)", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; LG; LG-C900)", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; LG; LG-C900k)", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; LG; LG-E900)", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; LG; LG-E900; Orange)", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; LG; LG-E900h)", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; LG; LG-Optimus 7)", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.0.1; ja-jp; L-06C Build/HRI66) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.0; en-us; LG-V900 Build/HRI39) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.4.2; en-gb; LG-V700 Build/KOT49I.A1403851714) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.1599.103 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; LG-V500 Build/KOT49I.V50020d) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.102 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.4.2; en-us; LG-V410/V41010d Build/KOT49I.V41010d) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.1599.103 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; LG-M257 Build/NRD90U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.126 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "LG", "user_agent": "Mozilla/5.0 (Linux; Android 8.0.0; LM-G710 AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2; xx-xx; HM NOTE 1W Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 MobilSafari/534.30 XiaoMi/MiuiBrowser/1.0", "mobile": true, "tablet": true}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.1; zh-cn; MI-ONE Plus Build/ITL41D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 XiaoMi/MiuiBrowser/1.0", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; zh-cn; MI 2SC Build/JRO03L) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 XiaoMi/MiuiBrowser/1.0", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; zh-cn; MI 2S Build/JRO03L) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 XiaoMi/MiuiBrowser/1.0", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; zh-tw; MI 1S Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 XiaoMi/MiuiBrowser/1.0", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.8; zh-cn; xiaomi2 Build/GRK39F) AppleWebKit/533.1 (KHTML, like Gecko)Version/4.0 MQQBrowser/4.4 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; es-es; MI 2A Build/miui.es JRO03L) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 XiaoMi/MiuiBrowser/1.0", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.1; zh-cn; MI 3 Build/JOP40D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 XiaoMi/MiuiBrowser/1.0", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; en-us; MI 1S Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 XiaoMi/MiuiBrowser/1.0", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; zh-cn; MI 3W Build/JLS36C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 XiaoMi/MiuiBrowser/1.0", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; zh-cn; HM 1SC Build/JLS36C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 XiaoMi/MiuiBrowser/1.0", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; en-us; HM 1SW Build/JLS36C) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/2.0.1", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; HM NOTE 1W Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/2.0.1", "mobile": true, "tablet": true}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; en-us; MI-ONE C1 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 XiaoMi/MiuiBrowser/1.0", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.4.4; en-us; MI 4W Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/2.0.1", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.4; MI PAD Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/534.24 (KHTML, like Gecko) Chrome/71.0.3578.141 Safari/534.24 XiaoMi/MiuiBrowser/12.6.6-gn", "mobile": false, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 10; POCOPHONE F1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.125 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 10; MI 8) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.125 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 10; Redmi Note 9s) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.125 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.2; en-gb; Redmi Note 5A Prime Build/N2G47H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/71.0.3578.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 12; tr-tr; Redmi Note 10 Build/SP1A.210812.016) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/100.0.4896.127 Mobile Safari/537.36 XiaoMi/MiuiBrowser/13.14.1-gn", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586", "mobile": false, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; ARM; Trident/6.0; Touch; .NET4.0E; .NET4.0C; Tablet PC 2.0)", "mobile": true, "tablet": true, "version": {"IE": "10.0", "Windows NT": "6.2", "Trident": "6.0"}}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; ARM; Trident/6.0)", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; ARM; Trident/6.0; Touch)", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; ARM; Trident/6.0; Touch; ARMBJS)", "mobile": true, "tablet": true}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; Win64; x64; Trident/6.0; Touch; MASMJS)", "mobile": false, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows NT 6.2; WOW64; rv:25.0) Gecko/20130626 Firefox/25.0", "mobile": false, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows NT 6.2; WOW64; rv:22.0) Gecko/20100101 Firefox/22.0", "mobile": false, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; Win64; x64; Trident/6.0; MDDCJS)", "mobile": false, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0; MDDCJS)", "mobile": false, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.52 Safari/537.36 OPR/15.0.1147.130", "mobile": false, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.116 Safari/537.36", "mobile": false, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; Win64; x64; Trident/6.0; Touch; MDDCJS; WebView/1.0)", "mobile": false, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; Microsoft; Lumia 640 XL LTE)", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; NOKIA; Lumia 520)", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; NOKIA; Lumia 620)", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; NOKIA; Lumia 822)", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 800)", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; Microsoft; Lumia 430 Dual SIM) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; Microsoft; Lumia 435) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; Microsoft; Lumia 532) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; Microsoft; Lumia 535) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; Microsoft; Lumia 535) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537 BMID/E679DAAB6F", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; Microsoft; Lumia 640 LTE) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 1520) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 520) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 520) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537 UCBrowser/4.2.1.541 Mobile", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 520; Vodafone) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 530) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 620) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 625) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 630 Dual SIM) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 630) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 635) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 635; Vodafone) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 735) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 820; Vodafone) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 830) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 925) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 930) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows NT 6.2; ARM; Trident/7.0; Touch; rv:11.0; WPDesktop; Lumia 635) like Gecko", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Microsoft; Lumia 550) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10586", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Microsoft; Lumia 640 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10586", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Microsoft; Lumia 950 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10586", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Microsoft; Lumia 950) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10586", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; NOKIA; Lumia 625) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10586", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; NOKIA; Lumia 635) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/13.10586", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; NOKIA; Lumia 830) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14295", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; NOKIA; Lumia 930) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14295", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows Phone 8.1; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 530 Dual SIM) like Gecko", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows NT 10.0; ARM; Lumia 950 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows NT 10.0; ARM; Lumia 950) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows NT 10.0; ARM; Lumia 930) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586", "mobile": true, "tablet": false}, {"vendor": "Microsoft", "user_agent": "Mozilla/5.0 (Windows NT 10.0; ARM; Lumia 730 Dual SIM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "MOT-W510/08.11.05R MIB/BER2.2 Profile/MIDP-2.0 Configuration/CLDC-1.1 EGE/1.0 UP.Link/6.3.0.0.0", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2.2; zh-cn; ME722 Build/MLS2GC_2.6.0) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.4; en-us; DROIDX Build/4.5.1_57_DX8-51) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.5; en-us; MB855 Build/4.5.1A-1_SUN-254_13) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.5; es-us; MB526 Build/4.5.2-51_DFL-50) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; en-ca; MB860 Build/4.5.2A-51_OLL-17.8) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.7; en-us; MOT-XT535 Build/V1.540) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.7; ko-kr; A853 Build/SHOLS_U2_05.26.3; CyanogenMod-7.1.2) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.0; en-us; Xoom Build/HRI39) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.1; en-us; Xoom Build/HMJ25) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; DROID RAZR 4G Build/6.7.2-180_DHD-16_M4-31) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; Xoom Build/IMM76L) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; pt-br; XT687 Build/V2.27D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false, "version": {"Android": "4.0.4", "Webkit": "534.30", "Safari": "4.0"}, "model": "XT687"}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; es-es; MOT-XT910 Build/6.7.2-180_SPU-19-TA-11.6) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; XT910 Build/9.8.2O-124_SPUL-17) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; XT915 Build/2_32A_2031) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; XT919 Build/2_290_2017) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.64 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; XT925 Build/9.8.2Q-50-XT925_VQLM-20) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.90 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; XT907 Build/9.8.1Q-66) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.90 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; XT901 Build/9.8.2Q-50_SLS-13) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; DROID BIONIC Build/9.8.2O-72_VZW-22) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; XT1022 Build/KXC20.82-14) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.4.4; en-in; XT1022 Build/KXC21.5-40) AppleWebKit/537.16 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.16", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; XT1025 Build/KXC20.82-13) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; XT1052 Build/KLA20.16-2.16.2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-gb; XT1052 Build/13.9.0Q2.X_83) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; XT1053 Build/13.9.0Q2.X_61) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; XT1053 Build/13.9.0Q2.X_55) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; XT1056 Build/13.9.0Q2.X-116-MX-17-6-2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.64 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; XT1031 Build/KXB20.9-1.10-1.18-1.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; XT1032 Build/KXB21.14-L1.40) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.4.2; de-de; XT1032 Build/KLB20.9-1.10-1.24-1.1) AppleWebKit/537.16 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.16", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; XT1034 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; en-us; XT1034 Build/14.10.0Q3.X-84-16) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; XT1035 Build/14.10.0Q3.X-23) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.59 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.3; XT1039 Build/KXB21.14-L1.31) AppleWebKit/537.36 (KHTML like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; en-us; XT919 Build/2_290_2002) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; XT919 Build/2_290_2004) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.90 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; en-us; XT920 Build/2_290_2014) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; XT920 Build/2_310_2014) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; XT905 Build/7.7.1Q_GCIRD-16) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Mobile Safari/537.22", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; XT908 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; XT897 Build/7.7.1Q-6_SPR-ASANTI_LE-18) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; XT1032 Build/LXB22.46-28.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.92 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; Moto E Build/LMY47V) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/39.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; XT1021 Build/KTU84Q) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; XT1068 Build/LXB22.46-28) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.85 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Motorola", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; XT1092 Build/MPE24.49-18) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.85 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; MPDC703 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; fr-fr; MPDC706 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; MPQC1010 Build/KVT49L) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Nexus", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 9 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Nexus", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; Nexus 9 Build/LMY48T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.81 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Nexus", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; Nexus 9 Build/N4F26M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.2987.132 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Nexus", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; Nexus 9 Build/NRD91N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.2987.132 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Nexus", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; Nexus 7 Build/JRO03D)", "mobile": true, "tablet": true}, {"vendor": "Nexus", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; Nexus 7 Build/JRO03D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19 QBWebViewUA/2 QBWebViewType/1 WKType/1", "mobile": true, "tablet": true}, {"vendor": "Nexus", "user_agent": "Mozilla/5.0 (Linux; U; Android 7.1.1; zh-CN; Nexus 6 Build/N6F27E) AppleWebKit/537.51.1 (KHTML, like Gecko) Version/4.0 Chrome/40.0.2214.89 UCBrowser/12.8.6.1274 Mobile Safari/537.36  AliApp(TUnionSDK/0.1.20.3)", "mobile": true, "tablet": false}, {"vendor": "Nexus", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.92 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)", "mobile": true, "tablet": false}, {"vendor": "Nexus", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Nexus", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; zh-cn; Galaxy Nexus Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/5.0 Mb2345Browser/9.4.1oem Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Nexus", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3694.0 Mobile Safari/537.36 Chrome-Lighthouse", "mobile": true, "tablet": false}, {"vendor": "Nexus", "user_agent": "E-Mobile7/7.0.37.20200806 (Linux;U;Android 10;Nexus One Build.FRG83) AppleWebKit/553.1(KHTML,like Gecko) Version/4.0 Mobile Safari/533.1  Language/zh Mode/PCKM80 DeviceBrand/OPPO IMEI/fa3d88953a07b469 Qiyuesuo/physicalSDK", "mobile": true, "tablet": false}, {"vendor": "Nexus", "user_agent": "Mozilla/5.0 (Linux; U; Android 5.1 en-us; Nexus One Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 baidubrowser/4.5.0.0 (Baidu; P1 5.1)", "mobile": true, "tablet": false}, {"vendor": "Nexus", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; Nexus 6 Build/MMB29K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/76.0.3809.89 Mobile Safari/537.36 T7/11.21 SP-engine/2.17.0 baiduboxapp/11.21.0.10 (Baidu; P1 6.0.1)", "mobile": true, "tablet": false}, {"vendor": "Nexus", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.4; en-us; Nexus 4 Build/JOP24G) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Nokia200/2.0 (12.04) Profile/MIDP-2.1 Configuration/CLDC-1.1 UCWEB/2.0 (Java; U; MIDP-2.0; en-US; nokia200) U2/1.0.0 UCBrowser/8.9.0.251 U2/1.0.0 Mobile UNTRUSTED/1.0", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Nokia6303iclassic/5.0 (06.61) Profile/MIDP-2.1 Configuration/CLDC-1.1 Mozilla/5.0 AppleWebKit/420+ (KHTML, like Gecko) Safari/420+", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "nokian73-1/UC Browser7.8.0.95/69/400 UNTRUSTED/1.0", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Nokia2760/2.0 (06.82) Profile/MIDP-2.1 Configuration/CLDC-1.1", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Nokia3650/1.0 SymbianOS/6.1 Series60/1.2 Profile/MIDP-1.0 Configuration/CLDC-1.0", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "NokiaN70-1/5.0737.3.0.1 Series60/2.8 Profile/MIDP-2.0 Configuration/CLDC-1.1/UC Browser7.8.0.95/27/352", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (S60V3; U; ru; NokiaN73) AppleWebKit/530.13 (KHTML, like Gecko) UCBrowser/8.6.0.199/28/444/UCWEB Mobile", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (S60V3; U; ru; NokiaC5-00.2)/UC Browser8.5.0.183/28/444/UCWEB Mobile", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (S60V3; U; ru; NokiaC5-00.2) AppleWebKit/530.13 (KHTML, like Gecko) UCBrowser/8.7.0.218/28/352/UCWEB Mobile", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (Series40; NokiaC3-00/08.63; Profile/MIDP-2.1 Configuration/CLDC-1.1) Gecko/20100401 S40OviBrowser/2.2.0.0.33", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Opera/9.80 (Series 60; Opera Mini/7.0.31380/28.2725; U; es) Presto/2.8.119 Version/11.10", "mobile": true, "tablet": false, "version": {"Opera Mini": "7.0.31380", "Presto": "2.8.119"}}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (Symbian/3; Series60/5.2 NokiaC7-00/025.007; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.3.1.37 Mobile Safari/533.4 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (Symbian/3; Series60/5.2 NokiaX7-00/022.014; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.3.1.37 Mobile Safari/533.4 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (Symbian/3; Series60/5.3 NokiaE6-00/************; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/535.1 (KHTML, like Gecko) NokiaBrowser/8.3.1.4 Mobile Safari/535.1 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (Symbian/3; Series60/5.3 NokiaC6-01/************; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/535.1 (KHTML, like Gecko) NokiaBrowser/8.3.1.4 Mobile Safari/535.1", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (Symbian/3; Series60/5.3 NokiaC6-01; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.4.2.6 Mobile Safari/533.4 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (Symbian/3; Series60/5.3 Nokia700/************; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.4.2.6 Mobile Safari/533.4 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (Symbian/3; Series60/5.3 Nokia700/************; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.4.1.14 Mobile Safari/533.4 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (Symbian/3; Series60/5.3 NokiaN8-00/************; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/535.1 (KHTML, like Gecko) NokiaBrowser/8.3.1.4 Mobile Safari/535.1 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (Symbian/3; Series60/5.3 Nokia701/************; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.4.2.6 Mobile Safari/533.4 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.2; U; Series60/3.1 Nokia6120c/3.83; Profile/MIDP-2.0 Configuration/CLDC-1.1 ) AppleWebKit/413 (KHTML, like Gecko) Safari/413", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.2; U; Series60/3.1 Nokia6120ci/7.02; Profile/MIDP-2.0 Configuration/CLDC-1.1 ) AppleWebKit/413 (KHTML, like Gecko) Safari/413", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.2; U; Series60/3.1 Nokia6120c/7.10; Profile/MIDP-2.0 Configuration/CLDC-1.1 ) AppleWebKit/413 (KHTML, like Gecko) Safari/413", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.2; U; Series60/3.1 NokiaE66-1/510.21.009; Profile/MIDP-2.0 Configuration/CLDC-1.1 ) AppleWebKit/413 (KHTML, like Gecko) Safari/413", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.2; U; Series60/3.1 NokiaE71-1/110.07.127; Profile/MIDP-2.0 Configuration/CLDC-1.1 ) AppleWebKit/413 (KHTML, like Gecko) Safari/413", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.2; U; Series60/3.1 NokiaN95-3/20.2.011 Profile/MIDP-2.0 Configuration/CLDC-1.1 ) AppleWebKit/413 (KHTML, like Gecko) Safari/413", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.2; U; Series60/3.1 NokiaE51-1/200.34.36; Profile/MIDP-2.0 Configuration/CLDC-1.1 ) AppleWebKit/413 (KHTML, like Gecko) Safari/413", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.2; U; Series60/3.1 NokiaE63-1/500.21.009; Profile/MIDP-2.0 Configuration/CLDC-1.1 ) AppleWebKit/413 (KHTML, like Gecko) Safari/413", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.2; U; Series60/3.1 NokiaN82/10.0.046; Profile/MIDP-2.0 Configuration/CLDC-1.1 ) AppleWebKit/413 (KHTML, like Gecko) Safari/413", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.3; Series60/3.2 NokiaE52-1/052.003; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/525 (KHTML, like Gecko) Version/3.0 BrowserNG/7.2.6.2", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.3; Series60/3.2 NokiaE52-1/@version@; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.3.1.26 Mobile Safari/533.4 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.3; Series60/3.2 NokiaC5-00/031.022; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/525 (KHTML, like Gecko) Version/3.0 BrowserNG/7.2.3.1", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.3; Series60/3.2 NokiaC5-00.2/081.003; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.3.1.32 Mobile Safari/533.4 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.3; U; Series60/3.2 NokiaN79-1/32.001; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/413 (KHTML, like Gecko) Safari/413", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.3; U; Series60/3.2 Nokia6220c-1/06.101; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/413 (KHTML, like Gecko) Safari/413", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.3; Series60/3.2 NokiaC5-00.2/071.003; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.3.1.26 Mobile Safari/533.4 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.3; Series60/3.2 NokiaE72-1/081.003; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.3.1.32 Mobile Safari/533.4 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.3; Series60/3.2 NokiaC5-00/061.005; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/525 (KHTML, like Gecko) Version/3.0 BrowserNG/7.2.6.2 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.4; Series60/5.0 NokiaX6-00/40.0.002; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.3.1.33 Mobile Safari/533.4 3gpp-gb", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.4; Series60/5.0 Nokia5800d-1/60.0.003; Profile/MIDP-2.1 Configuration/CLDC-1.1 AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.3.1.33 Mobile Safari/533.4 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.4; Series60/5.0 NokiaC5-03/12.0.023; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/525 (KHTML, like Gecko) Version/3.0 BrowserNG/7.2.6.9 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.4; Series60/5.0 Nokia5228/40.1.003; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/525 (KHTML, like Gecko) Version/3.0 BrowserNG/7.2.7.4 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.4; Series60/5.0 Nokia5230/51.0.002; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.3.1.33 Mobile Safari/533.4 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.4; Series60/5.0 Nokia5530c-2/32.0.007; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/525 (KHTML, like Gecko) Version/3.0 BrowserNG/7.2.6.9 3gpp-gba", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.4; Series60/5.0 NokiaN97-1/21.0.045; Profile/MIDP-2.1 Configuration/CLDC-1.1) AppleWebKit/525 (KHTML, like Gecko) BrowserNG/7.1.4", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (SymbianOS/9.4; Series60/5.0 NokiaN97-4/30.0.004; Profile/MIDP-2.1 Configuration/CLDC-1.1) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.3.1.28 3gpp-gba", "mobile": true, "tablet": false, "version": {"Symbian": "9.4", "Webkit": "533.4", "NokiaBrowser": "7.3.1.28"}, "model": "NokiaN97-4"}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Nokia; 7 Mozart T8698)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; 710)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Nokia; 800)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Nokia; 800C)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Nokia; 800C; Orange)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Nokia; 900)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Nokia; HD7 T9292)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; LG E-900)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 610)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 710)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 710; Orange)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 710; T-Mobile)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 710; Vodafone)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 800)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 800) UP.Link/5.1.2.6", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 800; Orange)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 800; SFR)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 800; T-Mobile)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 800; vodafone)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Nokia; Lumia 800c)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 900)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Nokia; Lumia 920)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; NOKIA; Lumia 920)", "mobile": true, "tablet": false, "version": {"IE": "10.0", "Windows Phone OS": "8.0", "Trident": "6.0"}, "model": "Lumia 920"}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Nokia; lumia800)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Nokia 610)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Nokia 710)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Nokia 800)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Nokia 800C)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Nokia 900)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Nokia; Nokia)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Nokia; SGH-i917)", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Nokia; TITAN X310e)", "mobile": true, "tablet": false, "version": {"Windows Phone OS": "7.5", "Trident": "5.0"}, "model": "TITAN X310e"}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 520) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; NOKIA; Lumia 635) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.135 Mobile Safari/537.36 Edge/12.10166", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; Microsoft; Lumia 535 Dual SIM) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537", "mobile": true, "tablet": false}, {"vendor": "Nokia", "user_agent": "NokiaC3-01.5/5.0 (07.58) Profile/MIDP-2.1 Configuration/CLDC-1.1 Mozilla/5.0 AppleWebKit/420+ (KHTML, like Gecko) Safari/420+", "mobile": true, "tablet": false}, {"vendor": "On<PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; V975i Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.108 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "On<PERSON>", "user_agent": "Mozilla/5.0 (Android; Tablet; rv:37.0) Gecko/37.0 Firefox/37.0", "mobile": true, "tablet": true}, {"vendor": "On<PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; V975m Core4 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "On<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; fr-fr; V975m Core4 Build/JSS15J) AppleWebKit/537.16 (KHTML, like Gecko) Version/4.0 Safari/537.16", "mobile": true, "tablet": true}, {"vendor": "On<PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; V975m Core4 Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.96 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "On<PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; V812 Core4 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.93 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "On<PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; V10 4G Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 YaBrowser/17.11.1.628.01 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "AdvanDigital", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; E1C Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "AdvanDigital", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; id-id; T3C Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Ainol", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.4; en-us; Ainol Novo8 Advanced Build/GRJ22) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Ainol", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; Novo10 Hero Build/20121115) AppleWebKit/535.19 (KHTML like Gecko) Chrome/18.0.1025.166 Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "Ainol", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; es-es; novo9-Spark Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "AllFine", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-gb; FINE7 GENIUS Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Amoi 8512/R18.0 NF-Browser/3.3", "mobile": true, "tablet": false, "model": "8512"}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; fr-fr; AN9G2I Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "AudioSonic", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-au; T-17B Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Blaupunkt", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; de-de; Endeavour 800NG Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Broncho", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2; es-es; Broncho N701 Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "bq", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; es-es; bq Livingstone 2 Build/1.1.7 20121018-10:33) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "bq", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; es-es; bq Edison Build/1.1.10-1015 20121230-18:00) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "bq", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; Maxwell Lite Build/v1.0.0.ICS.maxwell.20120920) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "bq", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; zh-tw; bq Maxwell Plus Build/1.0.0 20120913-10:39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "bq", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; Aquaris E10 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.93 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "bq", "user_agent": "Mozilla/5.0 (Linux; Android 5.1; Aquaris M10 Build/LMY47I) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.2526.83 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "bq", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Aquaris M10 FHD Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "bq", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Aquaris M8 Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.116 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "bq", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; Aquaris E5 Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.2987.132 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "bq", "user_agent": "Mozilla/5.0 (Linux; Android 5.0; Aquaris E5 HD Build/LRX21M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.91 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "bq", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Aquaris_M4.5 Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/51.0.2704.81 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "bq", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; Aquaris X5 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.91 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "bq", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; Aquaris E4 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.98 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "bq", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; Aquaris M5 Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.98 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Captiva", "user_agent": "Opera/9.80 (X11; Linux zvav; U; de) Presto/2.8.119 Version/11.10 Mozilla/5.0 (Linux; U; Android 4.1.1; de-de; CAPTIVA PAD 10.1 Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Casio", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.3; en-us; C771 Build/C771M120) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "ChangJia", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; pt-br; TPC97113 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "ChangJia", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; TPC7102 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Ce<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; en-us; Celkon CT 910+ Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Ce<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-in; CT-1 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Ce<PERSON><PERSON>", "user_agent": "CELKON.C64/R2AE SEMC-Browser/4.0.3 Profile/MIDP-2.0 Configuration/CLDC-1.1", "mobile": true, "tablet": false}, {"vendor": "Ce<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; Celkon A125 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Ce<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; en-US; Celkon*A86 Build/Celkon_A86) AppleWebKit/528.5+ (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1 UCBrowser/8.7.0.315 Mobile", "mobile": true, "tablet": false}, {"vendor": "Ce<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; Celkon A.R 40 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2; en-us; MID7010 Build/FRF85B) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; MID7048 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; MID8042 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Concorde", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; hu-hu; ConCorde Tab T10 Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Concorde", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; hu-hu; ConCorde tab PLAY Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Crest<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; nl-nl; CRESTA.CTP888 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "C<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; ru-ru; CUBE U9GT 2 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; es-es; Dslide 700 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.0.3", "Build": "IML74K", "Webkit": "534.30", "Safari": "4.0"}, "model": "Dslide 700"}, {"vendor": "DanyTech", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Genius Tab Q4 Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Digma", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; ru-ru; iDx10 3G Build/ICS.b02ref.20120331) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "DPS", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-us; DPS Dream 9 Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "ECS", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; it-it; TM105A Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.0.4", "Build": "IMM76D", "Webkit": "534.30"}}, {"vendor": "Eboda", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; ro-ro; E-Boda Supreme Dual Core X190 Build/JRO03C) AppleWebKit/534.30 (KHTML like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.1.1", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "Eboda", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; ro-ro; E-Boda Essential A160 Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Eboda", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; E-Boda Supreme X80 Dual Core Build/ICS.g12refM806A1YBD.20120925) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "Eboda", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; ro-ro; E-boda essential smile Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Eboda", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; ro-ro; E-Boda Supreme X80 Dual Core Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Eboda", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; ro-ro; E-Boda Supreme XL200IPS Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "E<PERSON>lio", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; Evolio X7 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "E<PERSON>lio", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; ro-ro; ARIA_Mini_wifi Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Fly", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; Fly IQ440; Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Fly", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; ru-ru; FLY IQ256 Build/GRK39F) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Fujitsu", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; ja-jp; F-10D Build/V21R48A) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.0.3", "Build": "V21R48A", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "Fujitsu", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; ru-ru; M532 Build/IML74K) AppleWebKit/534.30 (KHTML like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.0.3", "Build": "IML74K", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "FX2", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; FX2 PAD7 RK Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Galapad", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; zh-tw; G1 Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.1.1", "Webkit": "534.30", "Safari": "4.0", "Build": "JRO03C"}}, {"vendor": "GoClever", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; el-gr; GOCLEVER TAB A103 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "GoClever", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.4; zh-tw; A7GOCLEVER Build/GRJ22) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "GoClever", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; GOCLEVER TAB A104 Build/IMM76D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "GoClever", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; cs-cz; GOCLEVER TAB A93.2 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "GoClever", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; sk-sk; GOCLEVER TAB A971 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "GoClever", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; lv-lv; GOCLEVER TAB A972BK Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "GoClever", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; fr-fr; GOCLEVER TAB A104.2 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "GoClever", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; pt-pt; GOCLEVER TAB T76 Build/MID) AppleWebKit/534.30 (KHTML like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "GU", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; vi-vn; TX-A1301 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.0.4", "Build": "IMM76D", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "GU", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; da-dk; Q702 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.0.3", "Build": "IML74K", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "HCL", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; U1 Build/HCL ME Tablet U1) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "HCL", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; U1 Build/HCL ME Tablet U1) AppleWebKit/534.30 (KHTML like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "HCL", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; Connect-3G-2.0 Build/HCL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.72 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "HCL", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.3; pt-br; X1 Build/HCL ME Tablet X1) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Hisense", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; F5281 Build/KTU84Q) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/260.1985.135 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Hudl HT7S3 Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.82 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Iconbit", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; NT-3702M Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.72 Safari/537.36 OPR/16.0.1212.65583", "mobile": true, "tablet": true}, {"vendor": "Iconbit", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; ru-ru; NetTAB SPACE II Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "iJoy", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; fr-fr; Tablet Planet II-v3 Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Intenso", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1.;de-de; INM8002KP Build/JR003H) AppleWebKit/534.30 (KHTML, like Gecko)Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.1.1.", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "Intenso", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; TAB1004 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "INQ", "user_agent": "INQ1/R3.9.12 NF-Browser/3.5", "mobile": true, "tablet": false, "model": "INQ1"}, {"vendor": "Intex", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3; en-US; Cloud_X2 Build/MocorDroid4.0.1) AppleWebKit/528.5+ (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1 UCBrowser/9.2.0.419 Mobile", "mobile": true, "tablet": false}, {"vendor": "Intex", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; Cloud Y2 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Intex", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-in; Cloud X5 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "IRU", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; ru-ru; M702pro Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "JXD", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; F3000 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Karbonn", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; ST10 Build/JRO03C) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "KRAMER", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; KT107 Build/LMY47I) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.137 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.0; en-us;) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 (Kobo Touch)", "mobile": true, "tablet": true, "version": {"Android": "2.0", "Webkit": "533.1", "Safari": "4.0"}}, {"vendor": "Megafon", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.5; ru-ru; MegaFon V9 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Megafon", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; MT7A Build/JRO03C) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Safari/537.31", "mobile": true, "tablet": true}, {"vendor": "Mediacom", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; M-MPI10C3G Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.137 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Mediacom", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; M-SP10HXAH Build/MRA58K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.125 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "MediaTek", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; fr-fr; MT8377 Build/JRO03C) AppleWebKit/534.30 (KHTML like Gecko) Version/4.0 Safari/534.30/4.05d.1002.m7", "mobile": true, "tablet": true}, {"vendor": "Micromax", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; Micromax A110 Build/JRO03C) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Mobile Safari/537.22", "mobile": true, "tablet": false, "version": {"Android": "4.1.1", "Build": "JRO03C", "Webkit": "537.22", "Chrome": "25.0.1364.169"}}, {"vendor": "Micromax", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0; xx-xx; Micromax P250(Funbook) Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Webkit": "534.30", "Android": "4.0", "Build": "IMM76D", "Safari": "4.0"}}, {"vendor": "Modecom", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; pl-pl; FreeTAB 1014 IPS X4+ Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "MSI", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.5; el-gr; MSI Enjoy 10 Plus Build/1.2) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2.1; en-us; NABI-A Build/MASTER) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "NEC", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; ja-jp; N-08D Build/A5001911) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "NEC", "user_agent": "Mozilla/5.0 (Linux; U; Android AAA; BBB; N-06D Build/CCC) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Nexo", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; pl-pl; NEXO 3G Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30 ACHEETAHI/2100050074", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; zh-tw; Nibiru H1 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 XiaoMi/MiuiBrowser/1.0", "mobile": true, "tablet": false}, {"vendor": "Nook", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2.1; en-us; NOOK BNRV200 Build/ERD79 1.4.3) Apple WebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true, "version": {"Android": "2.2.1", "Webkit": "533.1", "Safari": "4.0"}}, {"vendor": "Nook", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; NOOK BNTV400 Build/ICS) AppleWebKit/534.30 (KHTML like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.0.4", "Build": "ICS", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "Nook", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; BNTV600 Build/IMM76L) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Safari/537.36 Hughes-PFB/CID5391275.AID1376709964", "mobile": true, "tablet": true, "version": {"Android": "4.0.4", "Build": "IMM76L", "Webkit": "537.36", "Chrome": "28.0.1500.94"}}, {"vendor": "Oneplus", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; A0001 Build/JLS36C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Oneplus", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; xx-xx; A0001 Build/JLS36C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Oneplus", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; ONEPLUS A5000 Build/NMF26X; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/60.0.3112.116 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Oneplus", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; ONEPLUS A5010 Build/OPM1.171019.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 Mobile Safari/537.36 [FB_IAB/Orca-Android;FBAV/164.0.0.24.95;]", "mobile": true, "tablet": false}, {"vendor": "Oneplus", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; ONEPLUS A3010) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.185 Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Oneplus", "user_agent": "Mozilla/5.0 (Linux; Android 12; KB2003) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; de-de; LOOX Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; de-de; LOOX Plus Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.X; de-de; XENO10 Build/ODYS XENO 10) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2.2; de-de; ODYS Space Build/I700T_P7_T04_TSCL_FT_R_0_03_1010_110623) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; de-de; ODYS-EVO Build/ODYS-EVO) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.X; de-de; Xelio 10 Pro Build/ODYS_Xelio) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; NEO_QUAD10 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.X; de-de; Xelio10Pro Build/ODYS_Xelio) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.1; en-us; ODYS-Xpress Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; de-de; XELIO7PHONETAB Build/IMM76I) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; XELIO10EXTREME Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; de-de; XELIO Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30 Mobile UCBrowser/3.2.1.441", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; XELIOPT2 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; nl-nl; ODYS-NOON Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "OverMax", "user_agent": "OV-SteelCore(B) Mozilla/5.0 (iPad; CPU OS 5_0_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9A405 Safari/7534.48.3", "mobile": true, "tablet": true}, {"vendor": "OverMax", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; pl-pl; OV-SteelCore Build/ICS.g08refem611.20121010) AppleWebKit/534.30 (KHTML like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "OverMax", "user_agent": "Mozilla/5.0 (Linux; Android 7.0.99; Qualcore 1027 4G Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.116 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "YONESTablet", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; pl-pl; BC1077 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Pantech", "user_agent": "PANTECH-C790/JAUS08312009 Browser/Obigo/Q05A Profile/MIDP-2.0 Configuration/CLDC-1.1", "mobile": true, "tablet": false}, {"vendor": "Pantech", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2.1; ko-kr; SKY IM-A600S Build/FRG83) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Pantech", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.3; en-us; ADR8995 4G Build/GRI40) AppleWebKit/533.1 (KHTML like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Pantech", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.2.1; en-us; PantechP4100 Build/HTK75) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true}, {"vendor": "Philips", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; Philips W336 Build/IMM76D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.45 Mobile Safari/537.36 OPR/15.0.1162.59192", "mobile": true, "tablet": false}, {"vendor": "Philips", "user_agent": "Philips_T3500/V1 Linux/3.4.5 Android/4.2.2 Release/03.26.2013 Browser/AppleWebKit534.30 Mobile Safari/534.30 MBBMS/2.2 System/Android 4.2.2;", "mobile": true, "tablet": false}, {"vendor": "Philips", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Philips W3568 Build/Philips_W3568) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Philips", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; Philips W832 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Philips", "user_agent": "Mozilla/5.0 (Linux;U;Android 4.2.2;es-us;Philips S388 Build/JDQ39) AppleWebkit/534.30 (HTML,like Gecko) Version/4.0 Mobile Safari/534.30;", "mobile": true, "tablet": false}, {"vendor": "Philips", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; Philips W536 Build/IMM76D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Philips", "user_agent": "Mozilla/5.0 (Linux;U;Android 4.2.2;es-us;Philips S308 Build/JDQ39) AppleWebkit/534.30 (HTML,like Gecko) Version/4.0 Mobile Safari/534.30;", "mobile": true, "tablet": false}, {"vendor": "Philips", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; ru-ru; Philips-W8500 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Philips", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; ru; Philips W8510 Build/JDQ39) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 UCBrowser/9.8.9.457 U3/0.8.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Philips", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; ru-RU; Philips W3568 Build/Philips W3568) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.1 Mobile Safari/534.30;", "mobile": true, "tablet": false}, {"vendor": "Philips", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; Philips S388 Build/JDQ39) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Philips", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; Build/PI3100.00.00.24) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Philips", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; W732 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Philips", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.1; PI7100_93 Build/PI7100.C.00.00.11) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.92 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "PocketBook", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.7; en-ru; PocketBook A10 3G Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "PointOfView", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-us; POV_TAB-PROTAB30-IPS10 Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Polaroid", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; MID4X10 Build/LMY47V) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.95 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Praktica", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; TP750 3GGSM Build/IMM76I) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.93 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "PROSCAN", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-us; PLT8088 Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.1.1", "Build": "JRO03H", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "PyleAudio", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; PTBL92BC Build/IMM76D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Mobile Safari/537.36", "mobile": true, "tablet": true, "version": {"Android": "4.0.4", "Build": "IMM76D", "Webkit": "537.36", "Chrome": "31.0.1650.59"}}, {"vendor": "Realme", "user_agent": "Mozilla/5.0 (Linux; U; Android 10; en-us; RMX1919 Build/QKQ1.200209.002) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/70.0.3538.80 Mobile Safari/537.36 HeyTapBrowser/********", "mobile": true, "tablet": false}, {"vendor": "RockChip", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2.1; hu-hu; RK2818, Build/MASTER) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "RockChip", "user_agent": "Mozilla/5.0 (Linux; U; Android Android 2.1-RK2818-1.0.0; zh-cn; MD701 Build/ECLAIR) AppleWebKit/530.17 (KHTML like Gecko) Version/4.0 Mobile Safari/530.17", "mobile": true, "tablet": true}, {"vendor": "RossMoor", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.1; ru-ru; RM-790 Build/JOP40D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "QMobile", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; en-us; A2 Build/GRK39F) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.4; de-de; SP-80 Build/GRJ22) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Skk", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1; en-us; CYCLOPS Build/F10) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Storex", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; eZee_Tab903 Build/JRO03H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Safari/537.36", "mobile": true, "tablet": true, "version": {"Android": "4.1.1", "Build": "JRO03H", "Webkit": "537.36"}}, {"vendor": "Storex", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; e<PERSON><PERSON>'Tab785 Build/JRO03C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.59 Safari/537.36", "mobile": true, "tablet": true, "version": {"Android": "4.1.1", "Build": "JRO03C", "Webkit": "537.36"}}, {"vendor": "Storex", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; e<PERSON><PERSON>'Tab971 Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19", "mobile": true, "tablet": true, "version": {"Android": "4.0.3", "Build": "IML74K", "Webkit": "535.19"}}, {"vendor": "Teclast", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; P98 3G\\xE5\\x85\\xAB\\xE6\\xA0\\xB8(A3HY) Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Teclast", "user_agent": "QQ\\xe9\\x9f\\xb3\\xe4\\xb9\\x90HD 4.0.1 (iPad; iPhone OS 8.0; zh_CN)", "mobile": true, "tablet": true}, {"vendor": "Teclast", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0; xx-xx; A15(E6C2) Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Teclast", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3; xx-xx; A10 Build/GRJ22) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Teclast", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; Teclast A10T Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Teclast", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; zh-cn; Teclast P85(A9D3) Build/IMM76D) AppleWebKit/534.30 (KHTML like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Teclast", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; A70H Build/JDQ39) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 UCBrowser/9.8.0.435 U3/0.8.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Tecno", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; TECNO P9 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Tecno", "user_agent": "Mozilla/5.0 (Linux; Android 5.1; TECNO DP8D Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.91 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Telstra", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.7; en-au; T-Hub2 Build/TVA301TELBG3) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "texet", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.4; ru-ru; TM-7021 Build/GB.m1ref.20120116) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true, "version": {"Android": "2.3.4", "Webkit": "533.1", "Safari": "4.0"}, "model": "TM-7021"}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; tolino tab 7 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.2.2", "Build": "JDQ39", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; tolino tab 8.9 Build/JDQ39) AppleWebKit/534.30 (KHTML like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.2.2", "Build": "JDQ39", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; tolino tab 7 Build/JDQ39) AppleWebkit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.57 Safari/537.36 OPR/18.0.1290.67495", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; tolino tab 7 Build/JDQ39) AppleWebkit/537.36 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Toshiba", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; TOSHIBA; TSUNAGI)", "mobile": true, "tablet": false}, {"vendor": "Toshiba", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2; it-it; TOSHIBA_FOLIO_AND_A Build/TOSHIBA_FOLIO_AND_A) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true, "version": {"Android": "2.2", "Webkit": "533.1", "Safari": "4.0"}}, {"vendor": "Trekstor", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; ST70408-1 Build/JDQ39) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Safari/537.31", "mobile": true, "tablet": true, "version": {"Android": "4.2.2", "Build": "JDQ39", "Webkit": "537.31", "Chrome": "26.0.1410.58"}}, {"vendor": "Trekstor", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; engb; Build/IMM76D) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9A405 Safari/7534.48.3 SurfTab_7.0", "mobile": true, "tablet": true}, {"vendor": "Trekstor", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; VT10416-2 Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Trekstor", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; de-de; ST10216-2A Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30;SurfTab_10.1", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; TM1088 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.126 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Ubislate", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; UBISLATE7C+ Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "WeVool", "user_agent": "Mozilla/5.0 (Linux; Android 5.0; WVT101 Build/LRX21M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.126 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Visture", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-gb; V97 HD Build/LR-97JC) Apple WebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Visture", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-us; Visture V4 Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Visture", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; ru-ru; Visture V4 HD Build/Visture V4 HD) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Visture", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; es-es; Visture V5 HD Build/Visture V5 HD) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Visture", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; ru-ru; Visture V10 Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Vivo", "user_agent": "Mozilla/5.0 (Linux; Android 11; vivo 1951) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Versus", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; VS-TOUCHPAD 9 Build/IMM76D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "Versus", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-gb; Versus Touchpad 9.7 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Versus", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-gb; CnM-TOUCHPAD7 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30 BMID/E67A45B1AB", "mobile": true, "tablet": true}, {"vendor": "Versus", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-gb; CnM TouchPad 7DC Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30 TwonkyBeamBrowser/3.3.5-95 (Android 4.1.1; rockchip CnM TouchPad 7DC Build/meizhi_V2.80.wifi8723.20121225.b11c800)", "mobile": true, "tablet": true}, {"vendor": "Versus", "user_agent": "OneBrowser/3.5/Mozilla/5.0 (Linux; U; Android 4.0.4; en-gb; TOUCHPAD 7 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Versus", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-gb; TOUCHTAB Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.1.1", "Build": "JRO03H", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "Viewsonic", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; de-de; ViewPad 10e Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Viewsonic", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2; it-it; ViewPad7 Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Viewsonic", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2.1; en-ca; ViewSonic VB733 Build/FRG83) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Viewsonic", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.2; en-gb; ViewPad7X Build/HTJ85B) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Viewsonic", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2; pt-br; ViewPad 10S Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Viewsonic", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-us; VB100a Pro Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-us; Sirius_Evo_QS Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-gb; Q8 Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Wolder", "user_agent": "Mozilla/5.0 (Linux; Android 4.4; miTab LIVE Build/KVT49L) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Wolder", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; pt-pt; miTab FUNK Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "<PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.1; nl-nl; AT-AS45q2 Build/JOP40D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Xoro", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; PAD 9720QR Build/PAD 9719QR) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Xoro", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; PAD720 Build/JDQ39) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "ZTE", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; V8200plus Build/IMM76I) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.166 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "ZTE", "user_agent": "Mozilla/5.0 (Linux; U; Android 7.1.2; zh-cn; Z999 Build/N2G47H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.132 MQQBrowser/8.4 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "ZTE", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.2; Z999 Build/N2G47H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2564.116 Mobile Safari/537.36 T7/10.11 light/1.0 baiduboxapp/10.11.0.13 (Baidu; P1 7.1.2)", "mobile": true, "tablet": false}, {"vendor": "ZTE", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.2; Z999 Build/N2G47H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.132 MQQBrowser/6.2 TBS/044403 Mobile Safari/537.36 MMWEBID/8089 MicroMessenger/6.7.3.1360(0x26070339) NetType/WIFI Language/zh_CN", "mobile": true, "tablet": false}, {"vendor": "ZTE", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; Z999 Build/OPM1.171019.019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/64.0.3282.137 Mobile Safari/537.36 NewsApp/49.0", "mobile": true, "tablet": false}, {"vendor": "Zync", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.3; en-us ; Z909 Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1/UCBrowser/8.4.1.204/145/444", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 11; zh-cn; Mi 10 Build/RKQ1.200826.002) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.116 Mobile Safari/537.36 XiaoMi/MiuiBrowser/15.8.6", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; Redmi 5A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; U; Android 11; zh-cn; M2012K11C Build/RKQ1.201112.002) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.116 Mobile Safari/537.36 XiaoMi/MiuiBrowser/15.7.20", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 10; Redmi Note 7 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.74 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 11; 21051182G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.85 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 11; 21081111RG) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Linux; Android 12; M2007J1SC) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.48 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Prestigio", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-gb; PMP5297C_QUAD Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Prestigio", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; sk-sk; PMP7100D3G Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.0.4", "Build": "IMM76D", "Webkit": "534.30", "Safari": "4.0"}, "model": "PMP7100D3G"}, {"vendor": "Prestigio", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; sk-sk; PMP7280C3G Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 MobilSafari/534.30", "mobile": true, "tablet": true}, {"vendor": "Prestigio", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; PMT3017_WI Build/KVT49L) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Prestigio", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; PMT3037_3G Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Prestigio", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; PMT5002_Wi Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.93 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Prestigio", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; el-gr; PMT5887_3G Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "MQQBrowser/4.0/Mozilla/5.0 (Linux; U; Android 3.2; zh-cn; GT-P6800 Build/HTJ85B) AppleWebKit/533.1 (KHTML, like Gecko) Mobile Safari/533.1", "mobile": true, "tablet": true, "version": {"MQQBrowser": "4.0"}}, {"vendor": "Samsung", "user_agent": "SAMSUNG-SGH-P250-ORANGE/P250BVHH8 Profile/MIDP-2.0 Configuration/CLDC-1.1 UP.Browser/6.2.3.3.c.1.101 (GUI) MMP/2.0", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "SAMSUNG-GT-B2710/B271MCXKF1 SHP/VPP/R5 Dolfin/2.0 QTV/5.3 SMM-MMS/1.2.0 profile/MIDP-2.1 configuration/CLDC-1.1 OPN-B", "mobile": true, "tablet": false, "version": {"Dolfin": "2.0"}}, {"vendor": "Samsung", "user_agent": "SAMSUNG-SGH-D900i/1.0 Profile/MIDP-2.0 Configuration/CLDC-1.1 UP.Browser/6.2.3.3.c.1.101 (GUI) MMP/2.0", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "SAMSUNG-GT-S5233T/S5233TXEJE3 SHP/VPP/R5 Jasmine/0.8 Qtv5.3 SMM-MMS/1.2.0 profile/MIDP-2.1 configuration/CLDC-1.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (SAMSUNG; SAMSUNG-GT-S5380D/S5380FXXKL3; U; Bada/2.0; ru-ru) AppleWebKit/534.20 (KHTML, like Gecko) Dolfin/3.0 Mobile HVGA SMM-MMS/1.2.0 OPN-B", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "SAMSUNG-GT-C3312/1.0 NetFront/4.2 Profile/MIDP-2.0 Configuration/CLDC-1.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 1.5; de-de; Galaxy Build/CUPCAKE) AppleWebKit/528.5 (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "SAMSUNG-GT-S3650/S3650XEII3 SHP/VPP/R5 Jasmine/1.0 Nextreaming SMM-MMS/1.2.0 profile/MIDP-2.1 configuration/CLDC-1.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "JUC (Linux; U; 2.3.6; zh-cn; GT-S5360; 240*320) UCWEB7.9.0.94/140/352", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (SAMSUNG; SAMSUNG-GT-S5250/S5250XEKJ3; U; Bada/1.0; ru-ru) AppleWebKit/533.1 (KHTML, like Gecko) Dolfin/2.0 Mobile WQVGA SMM-MMS/1.2.0 NexPlayer/3.0 profile/MIDP-2.1 configuration/CLDC-1.1 OPN-B", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/4.0 (compatible; MSIE 7.0; Windows Phone OS 7.0; Trident/3.1; IEMobile/7.0; SAMSUNG; SGH-i917)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (SAMSUNG; SAMSUNG-GT-S8530/S8530XXJKA; U; Bada/1.2; cs-cz) AppleWebKit/533.1 (KHTML, like Gecko) Dolfin/2.2 Mobile WVGA SMM-MMS/1.2.0 OPN-B", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 1.6; ru-ru; Galaxy Build/Donut) AppleWebKit/528.5+ (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.1-update1; ru-ru; GT-I5500 Build/ERE27) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2; en-us; GALAXY_Tab Build/MASTER) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2; ja-jp; SC-01C Build/FROYO) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2; fr-fr; GT-I9000 Build/FROYO) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.2.1; zh-cn; SCH-i909 Build/FROYO) UC AppleWebKit/534.31 (KHTML, like Gecko) Mobile Safari/534.31", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.3; ja-jp; SC-01C Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.3; en-gb; GT-P1000 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.3; el-gr; GT-I9001 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.3; en-ca; SGH-I896 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.4; es-us; GT-S5660L Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 MicroMessenger/4.5.1.261", "mobile": true, "tablet": false, "version": {"MicroMessenger": "4.5.1.261"}}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; en-us; GT-S5660 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; ru-ru; GT-S6102 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; pt-br; GT-S5367 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; fr-fr; GT-S5839i Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; en-gb; GT-S7500 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; en-gb; GT-S5830 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; es-us; GT-B5510L Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; pl-pl; GT-I9001-ORANGE/I9001BVKPC Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; en-us; GT-I8150 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; nl-nl; GT-I9070 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; en-gb; GT-S5360 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; es-us; GT-S6102B Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; en-us; GT-S5830i Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; ru-ru; GT-I8160 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; ru-ru; GT-S6802 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.7; ru-ru; GT-S5830 Build/GRWK74; LeWa_ROM_Cooper_12.09.21) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; ru-ru; GT-N7000 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.0.1; en-us; GT-P7100 Build/HRI83) AppleWebkit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.2; he-il; GT-P7300 Build/HTJ85B) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.2; en-gb; GT-P6200 Build/HTJ85B) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-gb; GT-I9100 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; GT-I9100G Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; nl-nl; GT-P5100 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android-4.0.3; en-us; Galaxy Nexus Build/IML74K) AppleWebKit/535.7 (KHTML, like Gecko) CrMo/16.0.912.75 Mobile Safari/535.7", "mobile": true, "tablet": false, "version": {"Chrome": "16.0.912.75"}}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; SGH-T989 Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false, "version": {"Chrome": "18.0.1025.166"}}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; GT-P5100 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; GT-I9300 Build/IMM76D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; SPH-D710 Build/IMM76I) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; zh-cn; GT-I9300 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-gb; GT-I9300-ORANGE/I9300BVBLG2 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; th-th; GT-I9300T Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; GT-I9100 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us ; GT-I9100 Build/IMM76D) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1/UCBrowser/8.4.1.204/145/355", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-gb; GT-N7000 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; th-th; GT-P6800 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; SAMSUNG-SGH-I747 Build/IMM76D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; es-es; GT-P5110 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; GT-P5110 Build/IMM76D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; zh-cn; SAMSUNG-GT-S7568_TD/1.0 Android/4.0.4 Release/07.15.2012 Browser/AppleWebKit534.30 Build/IMM76D) ApplelWebkit/534.30 (KHTML,like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false, "version": {"Android": "4.0.4"}}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; GT-P3100 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; tr-tr; GT-P3105 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-ca; GT-N8010 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; GT-S7562 Build/IMM76I) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-us; GT-N7100 Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; GT-N7100 Build/JZO54K) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.123 Mobile Safari/537.22 OPR/14.0.1025.52315", "mobile": true, "tablet": false, "version": {"Build": "JZO54K", "Webkit": "537.22", "Opera": "14.0.1025.52315"}}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; zh-hk; GT-N7105 Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; ru-ru; GT-N8000 Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.1; SGH-i747M Build/JRO03L) AppleWebKit/535.19(KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-gb; Galaxy Nexus - 4.1.1 - with Google Apps - API 16 - 720x1280 Build/JRO03S) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; GT-I8262 Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; it-it; Galaxy Nexus Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; en-us; SGH-I777 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; en-us; GT-S7710 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; en-us; GT-I9082 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; en-us; SGH-T999L Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; GT-P5210 Build/JDQ39) AppleWebKit/537.36 (KHTML, Like Gecko) Chrome/27.0.1453.90 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; en-us; SAMSUNG GT-I9200 Build/JDQ39) AppleWebKit/535.19 (KHTML, like Gecko) Version/1.0 Chrome/18.0.1025.308 Mobile Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; zh-cn; SCH-I959 Build/JDQ39) AppleWebKit/535.19 (KHTML, like Gecko) Version/1.0 Chrome/18.0.1025.308 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; nl-nl; SM-T310 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; en-us; SAMSUNG SM-P600 Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Version/1.5 Chrome/28.0.1500.94 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; en-gb; GT-N5100 Build/JDQ39) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; en-us; SAMSUNG SM-T530NU Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/1.5 Chrome/28.0.1500.94 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; SM-T800 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.135 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; fr-fr; SAMSUNG SM-T800 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/1.5 Chrome/28.0.1500.94 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; SM-T700 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.517 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; CETUS)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; Focus I917 By TC)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; Focus i917)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; FOCUS S)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; GT-I8350)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; GT-i8700)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; GT-S7530)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; Hljchm's Wp)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; I917)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; OMNIA 7)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; OMNIA7 By MWP_HS)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; OMNIA7)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; OMNIA7; Orange)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; SGH-i677)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; SGH-i917)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; SGH-i917.)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; SGH-i917R)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; SGH-i937)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; SMG-917R)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG_blocked_blocked_blocked; OMNIA7; Orange)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG_blocked_blocked_blocked_blocked; OMNIA7; Orange)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SUMSUNG; OMNIA 7)", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Windows NT 6.2; ARM; Trident/7.0; Touch; rv:11.0; WPDesktop; SAMSUNG; GT-I8750) like Gecko", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Windows NT 6.2; ARM; Trident/7.0; Touch; rv:11.0; WPDesktop; GT-I8750) like Gecko", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; en-gb; SAMSUNG GT-I9205 Build/JDQ39) AppleWebKit/535.19 (KHTML, like Gecko) Version/1.0 Chrome/18.0.1025.308 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; GT-P7510 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; SHV-E160K/VI10.1802 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; SM-T805 Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.92 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; SM-T116NQ Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.92 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; SM-G9250 Build/LRX22G; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/43.0.2357.121 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/35.0.0.48.273;]", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; SM-T705Y Build/KOT49H) AppleWebKit/537.36(KHTML, like Gecko) Chrome/42.0.2311.111 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; GT-I9505 Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Version/1.5 Chrome/28.0.1500.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; SM-T705 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.45 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; SM-T533 Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/33.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; SM-T533 Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.2403.133 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; SM-T357T Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.95 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; SM-T357T Build/LRX22G; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2564.106 Safari/537.36 [FB_IAB/FB4A;FBAV/66.0.0.33.73;]", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-G9350 Build/MMB29M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/46.0.2490.76 Mobile Safari/537.36 MicroMessenger/6.3.13.49_r4080b63.740 NetType/cmnet Language/zh_CN", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; 404SC Build/?buildID?) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.111 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; SM-T560 Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/40.0.2214.109 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; SM-T561 Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.95 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; en-ph; GT-S5300 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; SANSUNG SM-T555 Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/3.3 Chrome/38.0.2125.102 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "SAMSUNG-GT-C3310R/1.0 NetFront/4.2 Profile/MIDP-2.0 Configuration/CLDC-1.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.1; SAMSUNG SM-J120F Build/LMY47X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/3.5 Chrome/38.0.2125.102 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-T713 Build/MMB29M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/54.0.2840.68 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-T580 Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.68 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-T355Y Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.124 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG SM-T280 Build/LMY47V) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/3.5 Chrome/38.0.2125.102 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; SM-G920F Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.85 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; SM-G920F Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-G920V Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.98 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-G930F Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.85 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; SM-N910C Build/KTU84P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.85 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG-SM-T817A Build/LMY47X; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/52.0.2743.98 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG SM-T670 Build/LMY47X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/3.5 Chrome/38.0.2125.102 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; SM-T670 Build/LMY47X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG-SM-T677A Build/LMY47X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG-SM-T677A Build/LMY47X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/3.5 Chrome/38.0.2125.102 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; SM-T677V Build/LMY47X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.98 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-P580 Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-P580 Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/4.0 Chrome/44.0.2403.133 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-T587P Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-T587P Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/4.0 Chrome/44.0.2403.133 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; SAMSUNG SM-P350 Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/3.3 Chrome/38.0.2125.102 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-P350 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/4.0 Chrome/44.0.2403.133 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; SM-G950F Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; SM-G955F Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-T355Y Build/MMB29M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-T355Y Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/4.0 Chrome/44.0.2403.133 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; SAMSUNG SM-T355Y Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/3.2 Chrome/38.0.2125.102 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-T355 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.125 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-A310F Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/57.0.2987.132 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-A310F Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/4.0 Chrome/44.0.2403.133 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-A310F/A310FXXU2BQB1 Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/4.0 Chrome/44.0.2403.133 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-A310F Build/MMB29K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/58.0.3029.83 Mobile Safari/537.36 [FB_IAB/MESSENGER;FBAV/118.0.0.19.82;]", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-A310F/XXU2BQB6 Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/4.0 Chrome/44.0.2403.133 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; SM-A310F Build/LMY47X; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/58.0.3029.83 Mobile Safari/537.36 BingWeb/6.9.25207603", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-A310F Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/5.4 Chrome/51.0.2704.106 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; en-gb; SAMSUNG GT-I9190 Build/JDQ39) AppleWebKit/535.19 (KHTML, like Gecko) Version/1.0 Chrome/18.0.1025.308 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; GT-I9190 Build/MOB30Z) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.91 Mobile Safari/537.36 OPR/42.7.2246.114996", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-A510F/A510FXXU4BQD1 Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/5.4 Chrome/51.0.2704.106 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.0.2; SAMSUNG SM-T535/T535XXU1BQC1 Build/LRX22G) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/3.3 Chrome/38.0.2125.102 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-J500FN/J500FNXXU1BQE2 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/5.4 Chrome/51.0.2704.106 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.0; SAMSUNG SM-N9005/N9005XXSGBQA1 Build/LRX21V) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/5.4 Chrome/51.0.2704.106 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-G903F/G903FXXU1BQC1 Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/5.4 Chrome/51.0.2704.106 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; SAMSUNG SM-T285 Build/LMY47V) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/3.5 Chrome/38.0.2125.102 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-T585 Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SAMSUNG SM-T585 Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/4.0 Chrome/44.0.2403.133 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-T585 Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/5.2 Chrome/51.0.2704.106 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; SM-W708YZKAXTC  Build/LMY48Z) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/39.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; SM-G900F Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.2; SM-G950F Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/60.0.3112.116 Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; SAMSUNG-SM-N900A Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; SAMSUNG SM-J330F Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/6.4 Chrome/56.0.2924.87 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T835) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.136 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-T830 Build/PPR1.180610.011) AppleWebKit 537.36 (KHTML, like Gecko) SamsungBrowser/8.0 Chrome/63.0.4349.111 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; SAMSUNG SM-T837V Build/M1AJQ) AppleWebKit 537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9; SM-T720) AppleWebKit 537.36 (KHTML, like Gecko) SamsungBrowser/8.0 Chrome/74.0.3729.157 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9; SM-T510) AppleWebKit 537.36 (KHTML, like Gecko) SamsungBrowser/8.0 Chrome/74.0.3729.157 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T387V) AppleWebKit 537.36 (KHTML, like Gecko) SamsungBrowser/8.0 Chrome/74.0.3729.157 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; SM-G610F Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.91 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 10; SM-P610) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 10; SM-T290) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.81 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 10; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-T515) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/10.1 Chrome/71.0.3578.99 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9; SM-T590 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Safari/537.36 OPR/53.1.2569.142848", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-T590 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.2 Chrome/67.0.3396.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-T595 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9; SAMSUNG SM-T725 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/9.4 Chrome/67.0.3396.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-T817P Build/MMB29K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; SM-T817P Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.98 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; SM-P585N0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.90 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9; SM-T295) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.125 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 10; SAMSUNG SM-T865) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/11.2 Chrome/75.0.3770.143 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9; SM-T865) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 11; SM-T970) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.152 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; SM-G892A Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/67.0.3396.87 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9; SM-A530F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 10; SM-T860 Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.163 Whale/1.0.0.0 Crosswalk/25.80.14.7 Safari/537.36 NAVER(inapp; search; 710; 10.23.5)", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; SM-T380) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Mobile Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T380C Build/M1AJQ; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/045118 Mobile Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9; SM-T380 Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/63.0.3239.83 Mobile Safari/537.36 T7/11.11 baiduboxapp/11.11.0.12 (Baidu; P1 9)", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; U; Android 9; zh-<PERSON><PERSON>; SM-T380 Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.108 UCBrowser/12.4.0.1020 Mobile Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; SM-T231 Build/KOT49H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/48.0.2564.116 Safari/537.36 baiduboxapp/8.6.5 (Bai<PERSON>; P1 4.4.2)", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; zh-cn; SAMSUNG SM-T231 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Version/1.5 Chrome/28.0.1500.94 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 10; SM-T500) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.152 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.2; SM-G988N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.85 Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; SM-T536) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.85 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; SM-T837A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.80 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-G781B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Mobile Safari/537.36 EdgA/105.0.1343.50", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.2; SM-A805N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9; SM-G965F Build/PPR1.180610.011;) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/106.0.5249.79 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 11; SM-X200) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-T220) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 11; SM-T870) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.210 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-X906C Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.119 Mobile Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-F916B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.41 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 10; SM-F9160 Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/76.0.3809.89 Mobile Safari/537.36 T7/12.4 SP-engine/2.21.0 matrixstyle/0 lite baiduboxapp/5.1.6.10 (Baidu; P1 10) NABar/1.0", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.3396.87 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-F926B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/16.2 Chrome/92.0.4515.166 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-F926B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/17.0 Chrome/96.0.4664.104 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 11; SM-F926B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.61 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 11; SM-F926B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SAMSUNG SM-F916B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/17.0 Chrome/96.0.4664.104 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SAMSUNG SM-F926B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/17.0 Chrome/96.0.4664.104 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SAMSUNG SM-F926U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/18.0 Chrome/99.0.4844.88 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-F916B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.75 Mobile Safari/537.36 ABB/3.0.1", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-F916U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.98 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-F926B Build/SP1A.210812.016; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/104.0.5112.69 Mobile Safari/537.36 EdgW/1.0", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-F926B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-F926U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.58 Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-F926U1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-F936B Build/SP2A.220305.013; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/105.0.5195.136 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 9.0; SAMSUNG SM-F900U Build/PPR1.180610.011) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-X700) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.41 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-X706B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-X900) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-X706B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.104 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 13; SM-X806B Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/108.0.5359.128 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 13; SM-X706B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 13; SM-X706B Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/114.0.5735.130 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 12; SM-P613) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 13; SM-P613 Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/111.0.5563.58 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Samsung", "user_agent": "Mozilla/5.0 (Linux; Android 13; SAMSUNG SM-P613) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/21.0 Chrome/110.0.5481.154 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "SonyEricssonK800i/R1AA Browser/NetFront/3.3 Profile/MIDP-2.0 Configuration/CLDC-1.1", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.1-update1; es-ar; SonyEricssonE15a Build/2.0.1.A.0.47) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.1-update1; pt-br; SonyEricssonU20a Build/2.1.1.A.0.6) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.3; en-au; SonyEricssonX10i Build/3.0.1.G.0.75) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.4; ru-ru; SonyEricssonST18i Build/4.0.2.A.0.62) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.4; hr-hr; SonyEricssonST15i Build/4.0.2.A.0.62) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.4; sk-sk; SonyEricssonLT15i Build/4.0.2.A.0.62) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.7; th-th; SonyEricssonST27i Build/6.0.B.3.184) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.7; de-de; SonyEricssonST25i Build/6.0.B.3.184) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; pt-br; Xperia Tablet S Build/TID0092) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.0.3", "Build": "TID0092", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; LT18i Build/4.1.A.0.562) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; Sony Tablet S Build/TISU0R0110) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; es-es; Sony Tablet S Build/TISU0143) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-gb; SonyEricssonLT18i Build/4.1.B.0.587) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; fr-ch; SonyEricssonSK17i Build/4.1.B.0.587) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; SonyEricssonLT26i Build/6.1.A.2.45) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; LT22i Build/6.1.B.0.544) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; vi-vn; SonyEricssonLT22i Build/6.1.B.0.544) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; ST23i Build/11.0.A.5.5) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; ST23i Build/11.0.A.2.10) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.4; LT28h Build/6.1.E.3.7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-us; SGPT13 Build/TJDS0170) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; ja-jp; SonySO-03E Build/10.1.E.0.265) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true, "version": {"Android": "4.1.2", "Build": "10.1.E.0.265", "Webkit": "534.30", "Safari": "4.0"}}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; LT26w Build/6.2.B.1.96) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.72 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; SGP321 Build/10.3.1.A.0.33) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Safari/537.31", "mobile": true, "tablet": true, "version": {"Android": "4.2.2", "Build": "10.3.1.A.0.33", "Webkit": "537.31", "Chrome": "26.0.1410.58"}}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; C5303 Build/12.1.A.1.205) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.135 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; zh-cn; XL39h Build/14.2.A.1.136) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; sv-se; C5503 Build/10.1.1.A.1.273) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; en-us; C5502 Build/10.1.1.A.1.310) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; zh-cn; SonyL39t Build/14.1.M.0.202) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; zh-cn; L39u Build/14.1.n.0.63) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; zh-tw; M35c Build/12.0.B.5.37) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; M35c Build/12.0.B.2.42) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.90 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; zh-C<PERSON>; M35t Build/12.0.C.2.42) AppleWebKit/534.31 (KHTML, like Gecko) UCBrowser/9.3.2.349 U3/0.8.0 Mobile Safari/534.31", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; D6502 Build/17.1.A.2.69) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; D6503 Build/17.1.A.0.504) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; D6543 Build/17.1.A.2.55) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; D2004 Build/20.0.A.0.29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; en-gb; D2005 Build/20.0.A.1.12) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; D2104 Build/20.0.B.0.84) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; D2105 Build/20.0.B.0.74) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.170 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; pt-br; D2114 Build/20.0.B.0.85) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; D2302 Build/18.0.B.1.23) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.138 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; zh-cn; S50h Build/18.0.b.1.23) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 UCBrowser/9.6.3.413 U3/0.8.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; D2303 Build/18.0.C.1.13) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.138 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; D2305 Build/18.0.A.1.30) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.138 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; D2306 Build/18.0.C.1.7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.136 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; D5303 Build/19.0.1.A.0.207) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; D5306 Build/19.1.A.0.264) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.136 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; zh-CN; XM50h Build/19.0.D.0.269) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 UCBrowser/9.7.6.428 U3/0.8.0 Mobile Safari/533.1", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; zh-cn; XM50t Build/19.0.C.2.59) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; D5322 Build/19.0.D.0.253) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.131", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; zh-cn; M51w Build/14.2.A.1.146) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; M51w Build/14.2.A.1.146) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.136 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.1; D5102 Build/18.2.A.0.9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.1; D5103 Build/18.1.A.0.11) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.92 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.1; D5106 Build/18.1.A.0.11) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.3; en-gb; C6902 Build/14.2.A.1.136) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 GSA/3.2.17.1009776.arm", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; es-es; C6943 Build/14.1.G.2.257) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; C6943 Build/14.3.A.0.681) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; SGP412 Build/14.1.B.3.320) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1; en-us; SonySGP321 Build/10.2.C.0.143) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.1.2; SGP351 Build/10.1.1.A.1.307) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.3; SGP341 Build/10.4.B.0.569) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; SGP511 Build/17.1.A.2.36) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.122 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; SGP512 Build/17.1.A.2.36) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.122 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; fr-ch; SGP311 Build/10.1.C.0.344) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; en-us; SGP312 Build/10.1.C.0.344) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.4.2; de-de; SGP521 Build/17.1.A.2.69) AppleWebKit/537.16 (KHTML, like Gecko) Version/4.0 Safari/537.16", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.4.2; zh-cn; SGP541 Build/17.1.A.2.36) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; SGP551 Build/17.1.A.2.72) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/34.0.1847.114 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "SonyEricssonU5i/R2CA; Mozilla/5.0 (SymbianOS/9.4; U; Series60/5.0 Profile/MIDP-2.1 Configuration/CLDC-1.1) AppleWebKit/525 (KHTML, like Gecko) Version/3.0 Safari/525", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "SonyEricssonU5i/R2AA; Mozilla/5.0 (SymbianOS/9.4; U; Series60/5.0 Profile/MIDP-2.1 Configuration/CLDC-1.1) AppleWebKit/525 (KHTML, like Gecko) Version/3.0 Safari/525", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/4.0 (PDA; PalmOS/sony/model prmr/Revision:1.1.54 (en)) NetFront/3.0", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Opera/9.80 (Linux mips; U; InettvBrowser/2.2 (00014A;SonyDTV115;0002;0100) KDL40EX720; CC/BEL; en) Presto/2.7.61 Version/11.00", "mobile": false, "tablet": false}, {"vendor": "Sony", "user_agent": "Opera/9.80 (Linux armv7l; HbbTV/1.1.1 (; Sony; KDL32W650A; PKG3.211EUA; 2013;); ) Presto/2.12.362 Version/12.11", "mobile": false, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; es-ve; SonyST21a2 Build/11.0.A.6.5) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.2; D2533 Build/19.2.A.0.391) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 4.4.4; Xperia SP Build/KTU84Q) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.96 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla / 5.0 (Linux; Android 5.0.2; SOT31 Build / 28.0.D.6.71) AppleWebKit / 537.36 (KHTML, like Gecko) Chrome / 39.0.2171.93 Safari / 537.36", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; E5823 Build/32.0.A.4.11) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.76 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 5.0; E2303 Build/26.1.A.3.111) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.126 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; SGP771 Build/32.4.A.1.54) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.91 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 8.0.0; SOV34 Build/41.3.C.0.297; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/65.0.3325.109 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; 601SO Build/39.0.D.1.25; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/65.0.3325.109 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Sony", "user_agent": "Mozilla/5.0 (Linux; Android 7.1.1; F8332 Build/41.2.A.2.223) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.83 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Avant", "user_agent": "Mozilla/5.0 (Windows NT 6.1; WOW64; Trident/7.0; Avant Browser; rv:11.0) like Gecko", "mobile": false, "tablet": false}, {"vendor": "Avant", "user_agent": "Mozilla/5.0 (Windows NT 6.1; WOW64; Avant TriCore) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.101 Safari/537.36", "mobile": false, "tablet": false}, {"vendor": "Avant", "user_agent": "Mozilla/5.0 (Windows NT 5.1; rv:27.0; Avant TriCore) Gecko/20100101 Firefox/27.0", "mobile": false, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (<PERSON> WiiU) AppleWebKit/536.28 (KHTML, like Gecko) NX/3.0.3.12.14 NintendoBrowser/3.1.1.9577.EU", "mobile": false, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (<PERSON>) AppleWebKit/534.52 (KHTML, like Gecko) NX/{Version No} NintendoBrowser/{Version No}.US", "mobile": false, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Nintendo 3DS; U; ; en) Version/1.7567.US", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (Nintendo 3DS; U; ; en) Version/1.7498.US", "mobile": true, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (PLAYSTATION 3 4.21) AppleWebKit/531.22.8 (KHTML, like Gecko)", "mobile": false, "tablet": false}, {"vendor": "<PERSON><PERSON><PERSON>", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0; Xbox)", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Linux; Android 8.0.0; K10 G1 Build/O00623; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/58.0.3029.125 Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Linux; Android 8.1.0; GAAMII G1 Build/O11019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/61.0.3163.98 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Linux; U; Android 7.1.2; zh-cn; Z999 Build/N2G47H) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.132 MQQBrowser/8.4 Mobile Safari/537.36", "mobile": true, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.148 Safari/537.36 Vivaldi/1.4.589.41", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SV1; [eburo v4.0]; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; InfoPath.3; .NET4.0C; .NET4.0E)", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10) AppleWebKit/600.1.25 (KHTML, like Gecko) Version/8.0 Safari/600.1.25", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.111 Safari/537.36", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Iron/37.0.2000.0 Chrome/37.0.2000.0 Safari/537.36", "mobile": false, "tablet": false, "version": {"Iron": "37.0.2000.0"}}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Ubuntu Chromium/32.0.1700.102 Chrome/32.0.1700.102 Safari/537.36", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Windows NT 6.1; WOW64; rv:24.0) Gecko/20100101 Firefox/24.0", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Windows NT 6.1; WOW64; rv:18.0) Gecko/20100101 Firefox/18.0 AlexaToolbar/psPCtGhf-2.2", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:22.0) Gecko/20100101 Firefox/22.0", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (X11; Linux ppc; rv:17.0) Gecko/20130626 Firefox/17.0 Iceweasel/17.0.7", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (X11; Linux) AppleWebKit/535.22+ Midori/0.4", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Macintosh; U; Intel Mac OS X; en-us) AppleWebKit/535+ (KHTML, like Gecko) Version/5.0 Safari/535.20+ Midori/0.4", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.20 Safari/537.36  OPR/15.0.1147.18 (Edition Next)", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Windows NT 6.2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Windows NT 5.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Windows NT 5.2; WOW64; rv:21.0) Gecko/20100101 Firefox/21.0", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Opera/9.80 (Windows NT 5.2; WOW64) Presto/2.12.388 Version/12.14", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (X11; FreeBSD amd64; rv:14.0) Gecko/20100101 Firefox/14.0.1", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.2; Win64; x64; Trident/6.0; Touch; .NET4.0E; .NET4.0C; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET CLR 2.0.50727; Tablet PC 2.0; MASMJS)", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0; MANMJS)", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; Win64; x64; Trident/6.0; MASMJS)", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0; Touch; MASMJS)", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Opera/9.80 (Windows NT 6.2; WOW64; MRA 8.0 (build 5784)) Presto/2.12.388 Version/12.11", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; WOW64; Trident/6.0)", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Windows NT 6.3; Trident/7.0; rv 11.0) like Gecko", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; Touch; rv:11.0) like Gecko", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Mozilla/5.0 (Unknown; Linux armv7l) AppleWebKit/537.1+ (KHTML, like Gecko) Safari/537.1+ HbbTV/1.1.1 ( ;LGE ;NetCast 4.0 ;03.20.30 ;1.0M ;)", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "HbbTV/1.1.1 (;Panasonic;VIERA 2012;1.261;0071-3103 2000-0000;)", "mobile": false, "tablet": false}, {"vendor": "Other", "user_agent": "Opera/9.80 (Linux armv7l; HbbTV/1.1.1 (; Sony; KDL32W650A; PKG3.211EUA; 2013;); ) Presto/2.12.362 Version/12.11", "mobile": false, "tablet": false}, {"vendor": "TV", "user_agent": "Mozilla/5.0 (SMART-TV; Linux; Tizen 2.3) AppleWebkit/538.1 (KHTML, like Gecko) SamsungBrowser/1.0 TV Safari/538.1", "mobile": false, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:38.9) Gecko/20100101 Goanna/2.0 Firefox/38.9 PaleMoon/26.0.0", "mobile": false, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:2.0) Gecko/20100101 Goanna/20160121 PaleMoon/26.0.0", "mobile": false, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:38.9) Gecko/20100101 Goanna/2.0 Firefox/38.9 PaleMoon/26.1.1", "mobile": false, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Maemo; Linux; U; Jolla; Sailfish; Mobile; rv:31.0) Gecko/31.0 Firefox/31.0 SailfishBrowser/1.0", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Mae<PERSON>; Linux; U; Jolla; Sailfish; Tablet; rv:31.0) Gecko/31.0 Firefox/31.0 SailfishBrowser/1.0", "mobile": true, "tablet": true}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; U; Sailfish 3.0; Mobile; rv:38.0) Gecko/38.0 Firefox/38.0 SailfishBrowser/1.0", "mobile": true, "tablet": false, "version": {"Sailfish": "3.0", "SailfishBrowser": "1.0", "Gecko": "38.0"}}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; U; Sailfish 3.0; Tablet; rv:38.0) Gecko/38.0 Firefox/38.0 SailfishBrowser/1.0", "mobile": true, "tablet": true, "version": {"Sailfish": "3.0", "SailfishBrowser": "1.0", "Gecko": "38.0"}}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Mobile; rv:26.0) Gecko/26.0 Firefox/26.0", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Tablet; rv:26.0) Gecko/26.0 Firefox/26.0", "mobile": true, "tablet": true}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; CT1020W Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.94 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; Android 4.2.2; M6pro Build/JDQ39) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.141 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Generic", "user_agent": "MobileSafari/9537.53 CFNetwork/672.1.13 Darwin/13.1.0", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Appcelerator Titanium/3.2.2.GA (iPod touch/6.1.6; iPhone OS; en_US;)", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Opera Coast/3.0.3.78307 CFNetwork/672.1.15 Darwin/14.0.0", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; ALUMIUM10 Build/IML74K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.1; en-us; JY-G3 Build/JOP40D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; hu-hu; M758A Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; en-us; EVOTAB Build/IMM76I) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Generic", "user_agent": "Java/1.6.0_22", "mobile": false, "tablet": false, "version": {"Java": "1.6.0_22"}}, {"vendor": "Generic", "user_agent": "Opera/9.80 (Series 60; Opera Mini/6.5.29260/29.3417; U; ru) Presto/2.8.119 Version/11.10", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Opera/9.80 (Android; Opera Mini/6.5.27452/29.3417; U; ru) Presto/2.8.119 Version/11.10", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Opera/9.80 (iPhone; Opera Mini/7.1.32694/27.1407; U; en) Presto/2.8.119 Version/11.10", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.60 Safari/537.17 OPR/14.0.1025.52315", "mobile": false, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_3_2 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8H7 Safari/6533.18.5", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Opera/9.80 (Android 2.3.7; Linux; Opera Mobi/46154) Presto/2.11.355 Version/12.10", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (iPad; CPU OS 6_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B141 Safari/8536.25", "mobile": true, "tablet": true}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.2; en-us; sdk Build/MASTER) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2; en-us; sdk Build/JB_MR1) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Android; Mobile; rv:18.0) Gecko/18.0 Firefox/18.0", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/535.12 (KHTML, like Gecko) Maxthon/3.0 Chrome/18.0.966.0 Safari/535.12", "mobile": false, "tablet": false}, {"vendor": "Generic", "user_agent": "Opera/9.80 (Windows NT 5.1; U; Edition Yx; ru) Presto/2.10.289 Version/12.02", "mobile": false, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; Windows Phone 6.5.3.5)", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "PalmCentro/v0001 Mozilla/4.0 (compatible; MSIE 6.0; Windows 98; PalmSource/Palm-D061; Blazer/4.5) 16;320x320", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0)", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; Microsoft; XDeviceEmulator)", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; MAL; N880E; China Telecom)", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Opera/9.80 (Series 60; Opera Mini/7.0.29482/28.2859; U; ru) Presto/2.8.119 Version/11.10", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Opera/9.80 (S60; SymbOS; Opera Mobi/SYB-1202242143; U; en-GB) Presto/2.10.254 Version/12.00", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.3; en-au; 97D Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Generic", "user_agent": "Opera/9.80 (Android; Opera Mini/7.0.29952/28.2647; U; ru) Presto/2.8.119 Version/11.10", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Opera/9.80 (Android; Opera Mini/6.1.25375/28.2555; U; en) Presto/2.8.119 Version/11.10", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Opera/9.80 (Mac OS X; Opera Tablet/35779; U; en) Presto/2.10.254 Version/12.00", "mobile": true, "tablet": true}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Android; Tablet; rv:10.0.4) Gecko/10.0.4 Firefox/10.0.4 Fennec/10.0.4", "mobile": true, "tablet": true}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Android; Tablet; rv:18.0) Gecko/18.0 Firefox/18.0", "mobile": true, "tablet": true}, {"vendor": "Generic", "user_agent": "Opera/9.80 (Linux armv7l; Maemo; Opera Mobi/14; U; en) Presto/2.9.201 Version/11.50", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Opera/9.80 (Android 2.2.1; Linux; Opera Mobi/ADR-1207201819; U; en) Presto/2.10.254 Version/12.00", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; en-us; sdk Build/JRO03E) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30", "mobile": true, "tablet": false}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; Endeavour 1010 Build/ONDA_MID) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; de-de; Tablet-PC-4 Build/ICS.g08refem618.20121102) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Generic", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.2.2; de-de; Tagi Tab S10 Build/8089) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Bot", "user_agent": "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)", "mobile": false, "tablet": false}, {"vendor": "Bot", "user_agent": "grub-client-1.5.3; (grub-client-1.5.3; Crawl your own stuff with http://grub.org)", "mobile": false, "tablet": false}, {"vendor": "Bot", "user_agent": "Googlebot-Image/1.0", "mobile": false, "tablet": false}, {"vendor": "Bot", "user_agent": "Python-urllib/2.5", "mobile": false, "tablet": false}, {"vendor": "Bot", "user_agent": "facebookexternalhit/1.0 (+http://www.facebook.com/externalhit_uatext.php)", "mobile": false, "tablet": false}, {"vendor": "Bot", "user_agent": "AdsBot-Google (+http://www.google.com/adsbot.html)", "mobile": false, "tablet": false}, {"vendor": "Bot", "user_agent": "AdsBot-Google-Mobile (+http://www.google.com/mobile/adsbot.html) Mozilla (iPhone; U; CPU iPhone OS 3 0 like Mac OS X) AppleWebKit (KHTML, like Gecko) Mobile Safari", "mobile": true, "tablet": false}, {"vendor": "Bot", "user_agent": "Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/534.16 (KHTML, like Gecko, Google Keyword Suggestion) Chrome/10.0.648.127 Safari/534.16", "mobile": false, "tablet": false}, {"vendor": "Bot", "user_agent": "<PERSON><PERSON>", "mobile": false, "tablet": false}, {"vendor": "Bot", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 Safari/537.36 (+https://whatis.contentkingapp.com)", "mobile": false, "tablet": false}, {"vendor": "Bot", "user_agent": "Mozilla/5.0 (Linux; Android 7.0;) AppleWebKit/537.36 (KHTML, like Gecko) Mobile Safari/537.36 (compatible; AspiegelBot)", "mobile": true, "tablet": false}, {"vendor": "Verizon", "user_agent": "Mozilla/5.0 (Linux; Android 5.1.1; QTAQZ3 Build/LMY47V) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Verizon", "user_agent": "Mozilla/5.0 (Linux; Android 5.1; QTAIR7 Build/LMY47D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Verizon", "user_agent": "Mozilla/5.0 (Linux; Android 5.1; QTAIR7 Build/LMY47D; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/58.0.3029.83 Safari/537.36 [Pinterest/Android]", "mobile": true, "tablet": true}, {"vendor": "Verizon", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; QTASUN1 Build/MMB29M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Verizon", "user_agent": "Mozilla/5.0 (Linux; Android 7.0; QTASUN1 Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.116 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Verizon", "user_agent": "Mozilla/5.0(<PERSON><PERSON><PERSON><PERSON>;Android 5.1.1; QTAQTZ3 Build/LMY47V) AppleWebKit/537.36(KHTML, like Gecko) Chrome/59.0.3071.125 Safari/537.36", "mobile": true, "tablet": true}, {"vendor": "Vodafone", "user_agent": "Mozilla/5.0 (Linux; U; Android 3.2; hu-hu; SmartTab10-MSM8260-V02d-Dec022011-Vodafone-HU) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0", "mobile": true, "tablet": true}, {"vendor": "Vodafone", "user_agent": "Mozilla/5.0 (Linux; Android 4.0.3; SmartTabII10 Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19", "mobile": true, "tablet": true}, {"vendor": "Vodafone", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.1.1; fr-fr; SmartTAB 1002 Build/JRO03H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Vodafone", "user_agent": "Mozilla/5.0 (Linux; U; Android 4.0.4; de-de, SmartTabII7 Build/A2107A_A404_107_055_130124_VODA) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30", "mobile": true, "tablet": true}, {"vendor": "Vodafone", "user_agent": "Mozilla/5.0 (Linux; Android 6.0.1; VFD 1400 Build/MMB29M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/68.0.3440.91 Safari/537.36", "mobile": true, "tablet": true}]}