  req / hits / tag (444758 / 73555 / 44678) - user_agent (1041)
58989 / 6933 / 5240 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/6.0)
55308 / 9884 / 7995 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.64 Safari/537.31
36138 / 1681 / 1819 - Opera/9.80 (Windows NT 6.1; WOW64) Presto/2.12.388 Version/12.15
25387 / 4668 / 3863 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.64 Safari/537.31
23850 / 4623 / 3909 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:20.0) Gecko/20100101 Firefox/20.0
17258 / 1634 / 1391 - Mozilla/5.0 (Windows NT 6.1; rv:20.0) Gecko/20100101 Firefox/20.0
 9315 / 1535 / 1304 - Mozilla/5.0 (X11; Linux i686; rv:20.0) Gecko/20100101 Firefox/20.0
 7146 / 1271 / 1121 - Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.64 Safari/537.31
 6559 / 1234 /  991 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.64 Safari/537.31
 5639 / 1297 /  863 - Mozilla/5.0 (X11; Linux x86_64; rv:20.0) Gecko/20100101 Firefox/20.0
 5448 /    0 /    0 - msnbot-media/1.1 ( http://search.msn.com/msnbot.htm)
 5320 /  563 /  700 - Opera/9.80 (Windows NT 6.2; WOW64; MRA 6.0 (build 6011)) Presto/2.12.388 Version/12.15
 4948 /  910 /  593 - Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0)
 4719 /   78 /   75 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; InfoPath.1)
 4318 /  278 /  208 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB7.4; .NET CLR 1.1.4322; .NET CLR 3.0.04506.30; .NET CLR 3.0.04506.648; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET CLR 2.0.50727; .NET4.0C; .NET4.0E)
 4162 / 2554 /    3 - Mozilla/5.0 (compatible; Googlebot/2.1;  http://www.google.com/bot.html)
 4140 /  903 /  919 - Mozilla/5.0 (Windows NT 6.2; WOW64; rv:20.0) Gecko/20100101 Firefox/20.0
 3864 /  344 /  333 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36
 3812 /  807 /   11 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.43 Safari/537.31
 3799 /  563 /  496 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.63 Safari/537.31
 3251 /  601 /  629 - Opera/9.80 (Windows NT 6.1; Win64; x64) Presto/2.12.388 Version/12.15
 3202 /    0 /    0 - Mozilla/5.0 (compatible; YandexImages/3.0;  http://yandex.com/bots)
 3129 /  738 /  131 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1106.241 YaBrowser/1.5.1106.241 Safari/537.4
 3105 /  491 /  370 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.22 (KHTML, like Gecko) Ubuntu Chromium/25.0.1364.160 Chrome/25.0.1364.160 Safari/537.22
 3039 /  350 /  346 - Opera/9.80 (X11; Linux i686) Presto/2.12.388 Version/12.15
 3038 / 2351 /    0 - Mozilla/5.0 (compatible; bingbot/2.0;  http://www.bing.com/bingbot.htm)
 2971 /  392 /  399 - Opera/9.80 (Windows NT 6.2; WOW64) Presto/2.12.388 Version/12.15
 2766 /  147 /  172 - Opera/9.80 (Windows NT 6.1; U; ru) Presto/2.10.229 Version/11.60
 2764 /  739 /   17 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0
 2650 / 2044 /    0 - Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_1 like Mac OS X; en-us) AppleWebKit/532.9 (KHTML, like Gecko) Version/4.0.5 Mobile/8B117 Safari/6531.22.7 (compatible; Googlebot-Mobile/2.1;  http://www.google.com/bot.html)
 2514 /  304 /  262 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:14.0) Gecko/20100101 Firefox/14.0.1
 2500 /  306 /  134 - Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0)
 2494 /  250 /  256 - Opera/9.80 (Windows NT 6.1) Presto/2.12.388 Version/12.15
 2478 /  362 /  331 - Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:20.0) Gecko/20100101 Firefox/20.0
 2433 /  378 /  299 - Mozilla/5.0 (Windows NT 5.1; rv:20.0) Gecko/20100101 Firefox/20.0
 2369 /  234 /  127 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:21.0) Gecko/20100101 Firefox/21.0
 2269 /  488 /  348 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.97 Safari/537.22
 2137 /  405 /  256 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; 1A820D29-8445-92BE-2384-712BBCEDFDB1; MRSPUTNIK 2, 4, 1, 12; BTRS124447; GTB7.4; InfoPath.2; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET CLR 2.0.50727; AskTbFXTV5/5.8.0.12304)
 2050 /  391 /  464 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; InfoPath.2; .NET4.0C; .NET4.0E)
 1937 / 1676 /    0 - Mozilla/5.0 (compatible; YandexBot/3.0;  http://yandex.com/bots)
 1900 /    0 /    0 - Googlebot-Image/1.0
 1894 /  289 /  299 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.70 Safari/537.17
 1842 /  335 /  157 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36
 1836 /  354 /  298 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.43 Safari/537.31
 1829 /  176 /  171 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0)
 1608 /  260 /  181 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; WOW64; Trident/6.0)
 1594 /  273 /  195 - Opera/9.80 (X11; Linux x86_64) Presto/2.12.388 Version/12.14
 1543 / 1408 /    0 - Mozilla/5.0 (compatible; SearchBot)
 1511 /  176 /  115 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B329 Safari/8536.25
 1496 /  248 /  217 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.63 Safari/537.31
 1403 /  174 /  158 - Mozilla/5.0 (Windows NT 6.2) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.64 Safari/537.31
 1299 /  203 /  173 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.22 (KHTML, like Gecko) Iron/25.0.1400.0 Chrome/25.0.1400.0 Safari/537.22
 1220 /  206 /  207 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.70 Safari/537.17 YE
 1219 /  209 /  211 - Mozilla/5.0 (X11; Ubuntu; Linux i686; rv:20.0) Gecko/20100101 Firefox/20.0
 1210 /   81 /   80 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; GTB6.6; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0)
 1136 /  173 /  139 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/535.19 (KHTML, like Gecko) Ubuntu/11.04 Chromium/18.0.1025.151 Chrome/18.0.1025.151 Safari/535.19
 1125 /  194 /  158 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.22 (KHTML, like Gecko) Ubuntu Chromium/25.0.1364.160 Chrome/25.0.1364.160 Safari/537.22
 1068 /  173 /  130 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 6.0 (build 5680)) Presto/2.12.388 Version/12.13
 1040 /  202 /  207 - Mozilla/5.0 (X11; Linux i686; rv:21.0) Gecko/20100101 Firefox/21.0
 1032 /  206 /  176 - Opera/9.80 (Windows NT 6.1; WOW64; U; Edition Next; ru) Presto/2.11.310 Version/12.50
 1008 /  278 /  180 - Mozilla/5.0 (Windows NT 6.1; rv:2.0.1) Gecko/20100101 Firefox/4.0.1
  941 /  153 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.43 Safari/537.31
  929 /  181 /   88 - Mozilla/5.0 (iPad; CPU OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B329 Safari/8536.25
  896 /  148 /   44 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.43 Safari/537.31
  893 /  213 /    3 - Mozilla/5.0 (Windows NT 5.1; rv:19.0) Gecko/20100101 Firefox/19.0
  840 /  165 /   62 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.160 Safari/537.22
  825 /  143 /   67 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB7.4; InfoPath.2; .NET CLR 2.0.50727)
  821 /  170 /   85 - Mozilla/5.0 (Windows NT 6.0) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.64 Safari/537.31
  793 /   55 /   37 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; GTB7.4; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; eSobiSubscriber ********; .NET4.0C)
  781 /  183 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 Safari/537.22
  770 /  125 /   32 - Opera/9.80 (Windows NT 5.1) Presto/2.12.388 Version/12.14
  757 /    0 /    0 - contype
  755 /  159 /   94 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_3) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.65 Safari/537.31
  742 /   81 /   22 - Opera/9.80 (Windows NT 6.1; WOW64) Presto/2.12.388 Version/12.14
  709 /  180 /    0 - Mozilla/5.0 (X11; Linux x86_64; rv:19.0) Gecko/20100101 Firefox/19.0
  702 /  201 /  204 - Mozilla/5.0 (Windows NT 5.1; rv:14.0) Gecko/20100101 Firefox/14.0.1
  696 /  106 /  116 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C; .NET4.0E)
  685 /   76 /   21 - Mozilla/5.0 (iPad; CPU OS 5_1_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9B206 Safari/7534.48.3
  667 /  149 /  265 - Mozilla/5.0 (X11; Linux i686; rv:10.0.2) Gecko/20100101 Firefox/10.0.2
  648 /   51 /   49 - Opera/9.80 (Windows NT 5.1; U; ru) Presto/2.6.30 Version/10.60
  645 /  175 /   20 - Mozilla/5.0 (Windows NT 6.1; rv:19.0) Gecko/20100101 Firefox/19.0
  643 /   87 /   86 - Opera/9.80 (Windows NT 6.1; WOW64; Edition Yx) Presto/2.12.388 Version/12.15
  642 /   59 /   59 - Opera/9.80 (Windows NT 5.1; U; ru) Presto/2.5.24 Version/10.54
  642 /  122 /  124 - Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:21.0) Gecko/20100101 Firefox/21.0
  603 /   69 /   50 - Opera/9.80 (Windows NT 6.2; WOW64) Presto/2.12.388 Version/12.12
  596 /   64 /    0 - Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10.6; en-US; rv:1.9.2) Gecko/20100115 Firefox/3.6 (FlipboardProxy/0.0.5;  http://flipboard.com/browserproxy)
  593 /   30 /   39 - Opera/9.80 (Windows NT 6.1; WOW64; U; ru) Presto/2.10.289 Version/12.02
  589 /   72 /   14 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; MAAU; InfoPath.1)
  562 /   93 /   83 - Mozilla/5.0 (X11; Linux x86_64; rv:10.0.12) Gecko/20100101 Firefox/10.0.12 Iceweasel/10.0.12
  557 /   98 /   24 - Opera/9.80 (X11; Linux x86_64) Presto/2.12.388 Version/12.15
  545 /  108 /  137 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10.6; rv:20.0) Gecko/20100101 Firefox/20.0
  532 /   87 /   62 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.28 Safari/537.31
  519 /  280 /    0 - SolomonoBot/1.04 (http://www.solomono.ru)
  518 /  106 /   69 - Opera/9.80 (X11; Linux i686) Presto/2.12.388 Version/12.13
  518 /   95 /   28 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.46 Safari/537.31
  506 /   55 /   16 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0; SLCC1; .NET CLR 2.0.50727; Media Center PC 5.0; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET4.0C)
  476 /   66 /   67 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_5) AppleWebKit/536.29.13 (KHTML, like Gecko) Version/6.0.4 Safari/536.29.13
  437 /   61 /   18 - Opera/9.80 (Windows NT 5.1) Presto/2.12.388 Version/12.15
  435 /    0 /    0 - Mozilla/5.0 (compatible) Feedfetcher-Google; ( http://www.google.com/feedfetcher.html)
  434 /   56 /   51 - Opera/9.80 (Windows NT 6.1) Presto/2.12.388 Version/12.14
  431 /   28 /   29 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; MRSPUTNIK 2, 4, 1, 110; chromeframe/26.0.1410.64; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; InfoPath.1; InfoPath.2)
  431 /  422 /    0 - Mozilla/5.0 (Windows NT 6.1; rv:6.0) Gecko/20110814 Firefox/6.0 Google ( https://developers.google.com/ /web/snippet/)
  426 /   62 /    0 - Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.43 Safari/537.31
  422 /   62 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:********) Gecko/2010031422 Firefox/3.0.19 (.NET CLR 3.5.30729)
  417 /   56 /   21 - Opera/9.80 (Windows NT 6.1) Presto/2.12.388 Version/12.12
  401 /   64 /   64 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.65 Safari/537.36
  400 /  387 /    0 - Mozilla/5.0 (compatible; Linux i686; Yandex.Gazeta Bot/1.0;  http://gazeta.yandex.ru)
  397 /   85 /   60 - Mozilla/5.0 (X11; Linux x86_64; rv:20.0) Gecko/20100101 Firefox/20.0 Iceweasel/20.0
  387 /   50 /   49 - Mozilla/5.0 (Windows NT 6.1; rv:21.0) Gecko/20100101 Firefox/21.0
  379 /   71 /    0 - Opera/9.80 (Windows NT 6.1; WOW64) Presto/2.12.388 Version/12.11
  368 /   40 /   41 - Mozilla/5.0 (Windows NT 5.1; rv:20.0) Gecko/20100101 Firefox/20.0 SeaMonkey/2.17.1
  367 /   47 /   33 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_5) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.65 Safari/537.31
  366 /   24 /   24 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.1)
  366 /   85 /    0 - Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:19.0) Gecko/20100101 Firefox/19.0
  360 /   60 /   30 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; DefView)
  359 /   17 /   17 - Opera/9.80 (Android 2.3.3; Linux; Opera Mobi/ADR-1212030820) Presto/2.11.355 Version/12.10
  356 /   60 /   48 - Mozilla/5.0 (Linux; U; Android 2.2; ru-ru; HTC_Gratia_A6380 Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  355 /   18 /   20 - Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36
  352 /   51 /   49 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 Safari/537.22 CoolNovo/********
  348 /   74 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 Safari/537.22
  340 /   47 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 8.0 (build 6007)) Presto/2.12.388 Version/12.14
  340 /   95 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:18.0) Gecko/20100101 Firefox/18.0
  333 /   58 /   59 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1106.241 YaBrowser/1.5.1106.241 Safari/537.4
  311 /   21 /   16 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB7.4; InfoPath.2; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.04506.648; .NET CLR 3.5.21022; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C; .NET4.0E)
  310 /   30 /   39 - Mozilla/5.0 (Linux; Android 4.0.4; HTC Incredible S Build/IMM76D) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Mobile Safari/537.22
  308 /   47 /    0 - Mozilla/5.0 (Windows NT 6.1; rv:15.0) Gecko/20100101 Firefox/15.0.1
  306 /   18 /    0 - facebookexternalhit/1.1 ( http://www.facebook.com/externalhit_uatext.php)
  305 /   20 /   21 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36
  304 /   65 /   62 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.46 Safari/536.5
  303 /  293 /    0 - Mozilla/5.0 (compatible; Linux x86_64; Mail.RU_Bot/2.0;  http://go.mail.ru/help/robots)
  302 /   76 /    0 - Microsoft-WebDAV-MiniRedir/6.1.7601
  300 /   88 /   29 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:12.0) Gecko/20100101 Firefox/12.0
  285 /  184 /    0 - Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10.6; en-US; rv:1.9.2) Gecko/20100115 Firefox/3.6 (FlipboardProxy/1.1;  http://flipboard.com/browserproxy)
  282 /   35 /   40 - Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_3; en-us) AppleWebKit/533.16 (KHTML, like Gecko) Version/5.0 Safari/533.16
  282 /   58 /    5 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_3) AppleWebKit/536.28.10 (KHTML, like Gecko) Version/6.0.3 Safari/536.28.10
  276 /   22 /   25 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_3) AppleWebKit/536.29.13 (KHTML, like Gecko) Version/6.0.4 Safari/536.29.13
  273 /   12 /    0 - Mozilla/5.0 (X11; U; Linux x86_64; ru; rv:********) Gecko/2009061118 Fedora/3.0.11-1.fc9 Firefox/3.0.11
  267 /   27 /   14 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1271.95 Safari/537.11
  261 /   75 /   12 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_5_8) AppleWebKit/534.50.2 (KHTML, like Gecko) Version/5.0.6 Safari/533.22.3
  254 /    0 /    0 - Wget/1.11.4
  253 /   21 /   14 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B329
  250 /   24 /   14 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A523 Safari/8536.25
  250 /   57 /    0 - Mozilla/5.0 (Windows NT 6.0) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.43 Safari/537.31
  250 /  218 /    0 - msnbot/2.0b ( http://search.msn.com/msnbot.htm)
  244 /    0 /    0 - Googlebot/2.1 ( http://www.google.com/bot.html)
  240 /   25 /   33 - Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; 7 Mozart T8698)
  238 /  132 /   23 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.57 Safari/537.17
  233 /   63 /   39 - Mozilla/5.0 (X11; Linux x86_64; rv:17.0) Gecko/20100101 Firefox/17.0
  232 /   12 /   11 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36
  228 /   29 /   34 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_6_8) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.65 Safari/537.31
  219 /   34 /   10 - Opera/9.80 (Windows NT 5.1) Presto/2.12.388 Version/12.12
  219 /   26 /    2 - Mozilla/5.0 (Windows NT 6.1; rv:6.0) Gecko/20100101 Firefox/6.0
  216 /   41 /   45 - Mozilla/5.0 (Windows NT 6.2; WOW64; rv:22.0) Gecko/20130415 Firefox/22.0
  214 /   16 /   14 - Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0; XBLWP7; ZuneWP7)
  214 /   49 /   19 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.04506.648; .NET CLR 3.5.21022; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
  214 /   24 /    0 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.43 Safari/537.31
  213 /   13 /   14 - Mozilla/5.0 (X11; U; Linux x86_64; ru; rv:1.9.2.24) Gecko/20111109 CentOS/3.6.24-3.el6.centos Firefox/3.6.24
  207 /   26 /   26 - Mozilla/5.0 (Windows NT 6.2; rv:21.0) Gecko/20100101 Firefox/21.0
  206 /   38 /   69 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; MRSPUTNIK 2, 4, 0, 504; GTB7.4; BTRS99144; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; OfficeLiveConnector.1.3; OfficeLivePatch.0.0; InfoPath.2; Zune 4.7)
  203 /   23 /   23 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 5.9 (build 4947)) Presto/2.12.388 Version/12.15
  202 /   78 /    1 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; Win64; x64; Trident/6.0; Touch)
  201 /   60 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US) AppleWebKit/534.10 (KHTML, like Gecko) Chrome/8.0.552.215 Safari/534.10
  199 /   11 /   13 - Mozilla/5.0 (Linux; U; Android 2.3.4; ru-ru; HTC Sensation Z710e Build/GRJ22) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  197 /   93 /    0 - Mozilla/5.0 (compatible; Butterfly/1.0;  http://labs.topsy.com/butterfly/) Gecko/2009032608 Firefox/3.0.8
  197 /   41 /   42 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.2; Trident/4.0; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
  193 /   59 /    6 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; InfoPath.3; MS-RTC LM 8; BRI/2; .NET4.0E)
  193 /   13 /   12 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 1.1.4322)
  193 /   11 /   12 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/536.11 (KHTML, like Gecko) Chrome/20.0.1132.47 Safari/536.11
  186 /   45 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_5) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.43 Safari/537.31
  185 /  154 /    0 - SAMSUNG-SGH-E250/1.0 Profile/MIDP-2.0 Configuration/CLDC-1.1 UP.Browser/*******.c.1.101 (GUI) MMP/2.0 (compatible; Googlebot-Mobile/2.1;  http://www.google.com/bot.html)
  184 /   43 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10.8; rv:19.0) Gecko/20100101 Firefox/19.0
  184 /    6 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:9.0.1) Gecko/20100101 Firefox/9.0.1
  184 /   14 /   13 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/535.11 (KHTML, like Gecko) Chrome/17.0.963.47 Safari/535.11 MRCHROME
  183 /   61 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1;  http://www.changedetection.com/bot.html )
  182 /   15 /   15 - Opera/9.80 (Windows NT 5.1; U; ru) Presto/2.10.229 Version/11.64
  182 /   13 /   15 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1106.241 YaBrowser/1.5.1106.241 Safari/537.4
  179 /    8 /    9 - Mozilla/5.0 (X11; Linux x86_64; rv:16.0) Gecko/20100101 Firefox/16.0
  178 /   31 /   23 - Opera/9.80 (Windows NT 6.1; Edition Yx) Presto/2.12.388 Version/12.15
  178 /   24 /   27 - Mozilla/5.0 (iPad; CPU OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A523 Safari/8536.25
  177 /   17 /   18 - Opera/9.80 (Android 4.0.3; Linux; Opera Mobi/ADR-1301080958) Presto/2.11.355 Version/12.10
  177 /   31 /   33 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/535.2 (KHTML, like Gecko) Comodo_Dragon/******** Chrome/15.0.874.102 Safari/535.2
  176 /   43 /   45 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/536.5 (KHTML, like Gecko) Iron/19.0.1100.0 Chrome/19.0.1100.0 Safari/536.5
  175 /  148 /    0 - DoCoMo/2.0 N905i(c100;TB;W24H16) (compatible; Googlebot-Mobile/2.1;  http://www.google.com/bot.html)
  175 /   35 /   12 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 Safari/537.22
  174 /   19 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.3; .NET4.0C; .NET4.0E)
  172 /   52 /    0 - Opera/9.80 (Windows NT 6.0; U; en) Presto/2.10.289 Version/12.02
  170 /   21 /   32 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.7 (KHTML, like Gecko) Chrome/16.0.912.63 Safari/535.7
  170 /   27 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:6.0) Gecko/20100101 Firefox/6.0
  170 /   25 /    0 - Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0; MALC)
  167 /   27 /   30 - Mozilla/5.0 (X11; Ubuntu; Linux i686; rv:17.0) Gecko/20100101 Firefox/17.0
  167 /   33 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.2; .NET4.0C; .NET4.0E)
  165 /    9 /    9 - Mozilla/5.0 (iPad; CPU OS 6_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B141 Safari/8536.25
  164 /   19 /    0 - Mozilla/5.0 (Windows NT 6.0; rv:17.0) Gecko/20100101 Firefox/17.0
  163 /   19 /    1 - Mozilla/5.0 (Linux; Android 4.1.2; GT-I9300 Build/JZO54K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19
  162 /   54 /    2 - Opera/9.80 (Windows NT 6.1; Win64; x64) Presto/2.12.388 Version/12.14
  158 /   21 /   13 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:15.0) Gecko/20100101 Firefox/15.0
  156 /   39 /   11 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_5) AppleWebKit/536.28.10 (KHTML, like Gecko) Version/6.0.3 Safari/536.28.10
  155 /   18 /    8 - Opera/9.80 (Android 4.0.4; Linux; Opera Mobi/ADR-1301080958) Presto/2.11.355 Version/12.10
  154 /   50 /    1 - Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1106.241 YaBrowser/1.5.1106.241 Safari/537.4
  150 /   27 /   14 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_5) AppleWebKit/536.26.17 (KHTML, like Gecko) Version/6.0.2 Safari/536.26.17
  150 /    5 /    6 - Mozilla/5.0 (X11; Ubuntu; Linux i686; rv:21.0) Gecko/20100101 Firefox/21.0
  148 /   27 /    1 - Mozilla/5.0 (Windows NT 6.0; rv:19.0) Gecko/20100101 Firefox/19.0
  145 /   26 /    9 - Mozilla/5.0 (iPod; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B329 Safari/8536.25
  143 /   32 /    4 - Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0; BOIE9;RURU)
  142 /   13 /   11 - Mozilla/5.0 (Windows; U; Windows NT 6.1; ru; rv:*******) Gecko/20091102 Firefox/3.5.5
  142 /  121 /    1 - Java/1.7.0_21
  140 /   16 /    3 - Mozilla/5.0 (Windows NT 6.0; rv:20.0) Gecko/20100101 Firefox/20.0
  140 /   18 /    3 - Mozilla/5.0 (Windows NT 6.1; rv:12.0) Gecko/20100101 Firefox/12.0
  140 /    9 /   13 - Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_3; HTC_Sensation_Z710e; ru-ru) AppleWebKit/533.16 (KHTML, like Gecko) Version/5.0 Safari/533.16
  139 /   12 /    0 - Mozilla/5.0 (X11; U; Linux i686; ru; rv:*******4) Gecko/2009090216 Ubuntu/9.04 (jaunty) Firefox/3.0.14
  134 /   16 /   18 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/21.0.1180.57 Safari/537.1
  134 /    0 /    0 - Evernote Clip Resolver
  132 /   30 /    8 - Mozilla/5.0 (X11; Linux x86_64; rv:20.0) Gecko/20130409 Firefox/20.0
  132 /   17 /    0 - Mozilla/5.0 (Windows NT 6.2; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0
  132 /   38 /    0 - Wget/1.13.4 (linux-gnu)
  132 /   35 /    0 - Opera/9.80 (Windows NT 6.1; Edition Yx) Presto/2.12.388 Version/12.14
  132 /   13 /   15 - Mozilla/5.0 (iPod; CPU iPhone OS 6_1_2 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B146 Safari/8536.25
  132 /  113 /    0 - Mozilla/5.0 (compatible; Mail.RU_Bot/2.0;  http://go.mail.ru/help/robots)
  131 /   41 /    0 - Opera/9.80 (Windows NT 5.1; U; ru) Presto/2.10.229 Version/11.62
  131 /   15 /    1 - Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:*******) Gecko/20100401 Firefox/3.6.3 ( .NET CLR 3.5.30729; .NET4.0E)
  131 /  103 /    0 - UnwindFetchor/1.0 ( http://www.gnip.com/)
  130 /   27 /   19 - Opera/9.63 (Windows NT 6.0; U; ru) Presto/2.1.1
  128 /    9 /   12 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.56 Safari/537.17
  128 /   62 /    1 - -
  126 /    0 /   50 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/534  (KHTML, like Gecko) BingPreview/1.0b
  124 /   14 /   19 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET CLR 2.0.50727; .NET4.0C; .NET4.0E)
  122 /   25 /    2 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB7.4; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C; .NET4.0E; InfoPath.1)
  121 /   58 /    0 - Mozilla/5.0 (compatible; Ezooms/1.0; <EMAIL>)
  121 /    3 /    3 - Opera/9.80 (Android; Opera Mini/6.5.27452/29.3345; U; ru) Presto/2.8.119 Version/11.10
  120 /   24 /    9 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:6.0.1) Gecko/20100101 Firefox/6.0.1
  120 /    5 /    6 - Mozilla/5.0 (Linux; Android 4.1.2; GT-I9100 Build/JZO54K) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
  119 /   17 /   19 - Opera/9.80 (Windows NT 6.1; MRA 5.9 (build 4953)) Presto/2.12.388 Version/12.15
  118 /   46 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; InfoPath.3)
  118 /   35 /    8 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1271.95 Safari/537.11
  117 /   23 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_3) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.43 Safari/537.31
  116 /   20 /   24 - Mozilla/5.0 (X11; SunOS i86pc; rv:10.0.4) Gecko/20100101 Firefox/10.0.4
  115 /    6 /    2 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.4 (KHTML, like Gecko; Google Web Preview) Chrome/22.0.1229 Safari/537.4
  115 /   17 /   16 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.152 Safari/537.22
  112 /    9 /    0 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 Safari/537.22
  111 /   15 /   17 - Mozilla/5.0 (Linux; Android 4.0.3; HTC One V Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19
  111 /   13 /   16 - Opera/9.80 (Windows NT 6.0; MRA 5.5 (build 02842)) Presto/2.12.388 Version/12.14
  110 /   61 /    0 - Mozilla/5.0 (compatible; TourlentaScanner/0.6;  http://tourlenta.com)
  110 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; Trident/4.0; GTB7.4; .NET CLR 2.0.50727; .NET CLR 1.1.4322; .NET CLR 3.0.04506.30; InfoPath.1; .NET CLR 3.0.04506.648; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C; .NET4.0E)
  108 /   15 /    3 - Mozilla/5.0 (Windows NT 5.1; rv:10.0) Gecko/20100101 Firefox/10.0
  107 /   40 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_1) AppleWebKit/536.25 (KHTML, like Gecko) Version/6.0 Safari/536.25
  104 /   11 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:11.0) Gecko/20100101 Firefox/11.0
  104 /   13 /   13 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_6_8) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.43 Safari/537.31
  104 /   41 /    1 - Mozilla/5.0 (Windows NT 6.2) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.43 Safari/537.31
  103 /   80 /    0 - NING/1.0
  103 /   16 /   12 - Mozilla/5.0 (Linux; Android 4.1.2; GT-I9100 Build/JZO54K) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Mobile Safari/537.22
  103 /   30 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_5) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1106.240 YaBrowser/1.5.1106.240 Safari/537.4
  102 /   12 /   15 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_2) AppleWebKit/535.7 (KHTML, like Gecko) Chrome/16.0.912.75 Safari/535.7
  101 /   17 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.12 Safari/537.36
  101 /   14 /    2 - Mozilla/5.0 (Windows NT 6.1; rv:18.0) Gecko/20100101 Firefox/18.0
  101 /   15 /   16 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; InfoPath.2; .NET4.0C; .NET4.0E)
   98 /    2 /    2 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.21 (KHTML, like Gecko) QupZilla/1.4.3 Safari/537.21
   98 /    9 /    8 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_2 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B146
   96 /   14 /    5 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB7.4; .NET CLR 2.0.50727; .NET CLR 1.1.4322; .NET CLR 3.0.04506.30; InfoPath.1; .NET CLR 3.0.04506.648; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C; .NET4.0E)
   95 /   26 /    2 - Opera/9.80 (Windows NT 6.1; U; ru) Presto/2.7.62 Version/11.00
   94 /   21 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/534.57.2 (KHTML, like Gecko) Version/5.1.7 Safari/534.57.2
   93 /   22 /   22 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 1.1.4322; .NET CLR 2.0.50727; AskTbAVR-3/5.15.11.30498)
   92 /   36 /   36 - Mozilla/5.0 (X11; Linux x86_64; rv:21.0) Gecko/20100101 Firefox/21.0
   92 /   17 /    0 - Opera/9.80 (Windows NT 6.1; U; en) Presto/2.10.229 Version/11.62
   92 /   23 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; MRSPUTNIK 2, 4, 1, 162; MRA 5.5 (build 02842); SLCC1; .NET CLR 2.0.50727; Media Center PC 5.0; .NET CLR 3.0.30729)
   91 /   12 /   13 - Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 800)
   91 /   15 /   19 - Opera/9.80 (Windows NT 5.1) Presto/2.12.388 Version/12.10
   89 /    1 /    1 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 Safari/537.22 CoolNovo/********
   88 /   17 /   20 - Mozilla/5.0 (iPad; CPU OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A8426 Safari/8536.25
   88 /   45 /    0 - Mozilla/5.0 (compatible; vkShare;  http://vk.com/dev/Share)
   88 /   12 /   15 - Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 YaBrowser/1.7.1364.12390 Safari/537.22
   87 /   24 /   24 - Opera/9.80 (X11; Linux i686) Presto/2.12.388 Version/12.11
   87 /   11 /   12 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 6.0 (build 5976)) Presto/2.12.388 Version/12.15
   85 /    3 /    0 - Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:24.0) Gecko/20130525 Firefox/24.0
   85 /    2 /    2 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; MDDC; InfoPath.3; .NET4.0C)
   84 /    5 /    5 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10.8; rv:20.0) Gecko/20100101 Firefox/20.0
   83 /   27 /    8 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.70 Safari/537.17 YE
   82 /   10 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:15.0) Gecko/20100101 Firefox/15.0
   81 /   16 /    0 - Mozilla/5.0 (X11; Linux i686; rv:19.0) Gecko/20100101 Firefox/19.0
   81 /    9 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1)
   81 /    2 /    0 - Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 Safari/537.22
   81 /    6 /    8 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; .NET CLR 1.1.4322; .NET CLR 2.0.50727; InfoPath.2; .NET CLR 3.0.04506.30; .NET4.0C; .NET4.0E)
   80 /    9 /    9 - Mozilla/5.0 (Windows NT 5.1; rv:21.0) Gecko/20100101 Firefox/21.0
   79 /   11 /    0 - Mozilla/5.0 (iPad; CPU OS 5_1_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Mobile/9B206
   78 /    4 /    4 - Mozilla/5.0 (Linux; Android 4.2.2; Nexus 7 Build/JDQ39) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Safari/537.22
   78 /   20 /   21 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/534.24 (KHTML, like Gecko) Chrome/11.0.696.77 Safari/534.24
   78 /   26 /    0 - Mozilla/5.0 (Windows NT 6.2) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 Safari/537.22
   76 /    8 /   14 - Opera/9.80 (Windows NT 6.1; WOW64; Edition Rambler) Presto/2.12.388 Version/12.15
   75 /   56 /    0 - Mozilla/5.0 (compatible; MJ12bot/v1.4.3; http://www.majestic12.co.uk/bot.php? )
   74 /   18 /   19 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; Win64; x64; Trident/6.0)
   74 /    5 /   13 - Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
   74 /    9 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:1.9.0.5) Gecko/2008120122 Firefox/3.0.5 (.NET CLR 3.5.30729)
   74 /    4 /    1 - Opera/9.80 (Windows NT 6.1; U; ru) Presto/2.10.289 Version/12.02
   74 /    6 /    4 - Mozilla/5.0 (Linux; Android 4.1.1; HTC One X  Build/JRO03C) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
   74 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; MRSPUTNIK 2, 2, 0, 94; GTB7.4; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
   73 /    8 /    8 - Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; NOKIA; Lumia 920)
   72 /   40 /    0 - Mozilla/5.0 (compatible; YandexDirect/3.0;  http://yandex.com/bots)
   71 /   24 /    5 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0; MASMJS)
   70 /    2 /    1 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/535.11 (KHTML, like Gecko) Chrome/17.0.963.47 Safari/535.11 MRCHROME
   70 /   24 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; MRSPUTNIK 2, 4, 0, 484; MRA 6.0 (build 5970); .NET4.0E; AskTbFXTV5/5.14.1.20007; .NET4.0C)
   70 /   11 /    0 - Mozilla/5.0 (compatible; Yahoo! Slurp; http://help.yahoo.com/help/us/ysearch/slurp)
   69 /    6 /    8 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36
   69 /    5 /    8 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 5.10 (build 5282)) Presto/2.12.388 Version/12.15
   69 /    4 /    5 - Opera/9.80 (Android; Opera Mini/7.5.33286/29.3345; U; en) Presto/2.8.119 Version/11.10
   67 /   15 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET4.0C; .NET4.0E; Media Center PC 6.0; BRI/2; InfoPath.3)
   67 /   10 /    8 - Mozilla/5.0 (Windows NT 5.1; rv:15.0) Gecko/20100101 Firefox/15.0.1
   67 /   17 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_5) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.36 YaBrowser/2.0.1364.6141 Safari/537.22
   67 /   11 /    7 - Mozilla/5.0 (Linux; U; Android 4.1.2; ru-ru; GT-I9300 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
   67 /    3 /    3 - Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Win64; x64; Trident/5.0)
   65 /    6 /    1 - Mozilla/5.0 (X11; Ubuntu; Linux i686; rv:19.0) Gecko/20100101 Firefox/19.0
   65 /    8 /    0 - Mozilla/5.0 (Windows NT 6.1; rv:16.0; Avant TriCore) Gecko/20121105 Firefox/16.0
   64 /   16 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_3) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.63 Safari/537.31
   64 /    5 /    5 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) CriOS/26.0.1410.53 Mobile/10B329 Safari/8536.25
   64 /    9 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/534.30 (KHTML, like Gecko) Chrome/12.0.742.100 Safari/534.30
   64 /    4 /    6 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.69 Safari/537.17
   63 /    6 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 Safari/537.22
   63 /    6 /    0 - Mozilla/5.0 (X11; Linux x86_64; rv:18.0) Gecko/20100101 Firefox/18.0 Iceweasel/18.0.1
   63 /    0 /    0 - Microsoft Office Existence Discovery
   63 /    2 /    5 - Mozilla/5.0 (Linux; U; Android 2.3.5; en-ru; HTC_DesireS_S510e Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
   63 /   10 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; GTB7.4; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
   63 /    7 /   12 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 6.0 (build 6081)) Presto/2.12.388 Version/12.15
   62 /   43 /    0 - ia_archiver ( http://www.alexa.com/site/help/webmasters; <EMAIL>)
   60 /   17 /    0 - Opera/9.80 (Windows NT 5.2; Edition Yx) Presto/2.12.388 Version/12.14
   60 /    0 /    0 - Opera/9.80 (Series 60; Opera Mini/7.0.29482/29.3271; U; ru) Presto/2.8.119 Version/11.10
   59 /    3 /    4 - Opera/9.80 (J2ME/MIDP; Opera Mini/5.1.22296/29.3345; U; ru) Presto/2.8.119 Version/11.10
   58 /    0 /    2 - Opera/9.80 (Android; Opera Mini/7.5.32193/29.3345; U; ru) Presto/2.8.119 Version/11.10
   58 /    6 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB7.4; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30; .NET CLR 3.0.04506.648; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C; InfoPath.2; .NET4.0E)
   57 /    5 /    5 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.12 Safari/537.36
   57 /    7 /    0 - Mozilla/5.0 (Linux; Android 4.0.4; ZTE Grand Era Build/IMM76L) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.40 Mobile Safari/537.31 OPR/14.0.1074.54070
   57 /    5 /    7 - Mozilla/5.0 (Linux; Android 4.0.4; U8836D Build/HuaweiU8836D) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
   56 /    3 /    4 - Opera/9.80 (Windows NT 6.1; Edition Yx 01) Presto/2.12.388 Version/12.14
   56 /    4 /    4 - Mozilla/5.0 (Macintosh; U; Intel Mac OS X; ru-ru) AppleWebKit/535  (KHTML, like Gecko) Version/5.0 Safari/535.18  Midori/0.4
   55 /   11 /    1 - Mozilla/5.0 (iPad; CPU OS 6_1_2 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B146 Safari/8536.25
   55 /   13 /   14 - Opera/9.80 (Android 2.3.4; Linux; Opera Mobi/ADR-1301080958) Presto/2.11.355 Version/12.10
   54 /    4 /    4 - Opera/9.80 (Windows NT 6.1; Edition Yx 01) Presto/2.12.388 Version/12.11
   54 /   35 /    0 - ichiro/3.0 (http://search.goo.ne.jp/option/use/sub4/sub4-1/)
   54 /   20 /    0 - Microsoft-WebDAV-MiniRedir/6.1.7600
   54 /    5 /    7 - Mozilla/5.0 (Linux; U; Android 2.2.1; ru-ru; HTC Wildfire Build/FRG83D) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
   53 /   18 /    0 - Mozilla/5.0 (X11; Ubuntu; Linux i686; rv:15.0) Gecko/20100101 Firefox/15.0.1
   53 /    2 /    3 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.2; Trident/4.0; .NET CLR 1.1.4322; .NET CLR 2.0.50727; InfoPath.2; MS-RTC LM 8; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C; .NET4.0E)
   53 /   10 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_5) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.63 Safari/537.31
   53 /    9 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:13.0) Gecko/20100101 Firefox/13.0.1
   52 /    2 /    1 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.56 Safari/537.17 CoolNovo/2.0.6.12
   52 /    5 /    0 - Mozilla/5.0 (X11; Linux x86_64; rv:19.0) Gecko/20100101 Firefox/19.0 SeaMonkey/2.16.2
   52 /    2 /    4 - Opera/9.80 (Windows NT 6.2; WOW64; YZF) Presto/2.12.388 Version/12.15
   52 /    8 /    0 - Mozilla/5.0 (Linux; U; Android 3.2; ru-ru; GT-P7300 Build/HTJ85B) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13
   51 /   12 /   18 - Opera/9.80 (Windows NT 5.1; WOW64) Presto/2.12.388 Version/12.14
   51 /   11 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:16.0) Gecko/20100101 Firefox/16.0
   51 /   41 /    0 - Mozilla/5.0 (compatible; TweetedTimes Bot/1.0;  http://tweetedtimes.com)
   51 /    7 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0)
   51 /    5 /    0 - Mozilla/5.0 (Linux; U; Android 2.3.4; ru-ru; LG-E510 Build/GRJ22) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
   49 /   12 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_2) AppleWebKit/536.26.14 (KHTML, like Gecko) Version/6.0.1 Safari/536.26.14
   49 /    4 /    4 - Mozilla/5.0 (iPad; CPU OS 5_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Mobile/9B176
   48 /    4 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30)
   47 /   11 /    3 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_3) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.36 YaBrowser/2.0.1364.6141 Safari/537.22
   47 /   39 /    0 - Wotbox/2.01 ( http://www.wotbox.com/bot/)
   47 /    4 /    4 - Mozilla/5.0 (X11; Linux i686; rv:14.0) Gecko/20100101 Firefox/14.0.1
   46 /   12 /    2 - Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:16.0) Gecko/20100101 Firefox/16.0
   46 /    4 /    5 - Opera/9.80 (Windows NT 6.1; MRA 5.10 (build 5310)) Presto/2.12.388 Version/12.14
   45 /    3 /    4 - Mozilla/5.0 (Linux; Android 4.0.4; ARCHOS 80G9 Build/IMM76D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19
   45 /    2 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.97 Safari/537.22
   44 /    8 /    8 - Opera/9.80 (X11; Linux i686; U; en) Presto/2.10.289 Version/12.02
   43 /    0 /    0 - Microsoft Office Protocol Discovery
   43 /    1 /    2 - Mozilla/5.0 (X11; U; Linux i686; en-US; rv:********) Gecko/20120714 Iceweasel/3.5.16 (like Firefox/3.5.16)
   43 /   10 /    0 - Mozilla/5.0 (iPad; CPU OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) CriOS/25.0.1364.124 Mobile/10B329 Safari/8536.25
   42 /    5 /    5 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_4 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B350 Safari/8536.25
   42 /    3 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET4.0C; .NET4.0E; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; InfoPath.2)
   42 /    9 /    0 - Mozilla/5.0 (Windows NT 6.1; rv:17.0) Gecko/20100101 Firefox/17.0
   42 /   40 /    0 - Mozilla/5.0 (compatible; Genieo/1.0 http://www.genieo.com/webfilter.html)
   41 /    2 /    4 - Mozilla/5.0 (Linux; Android 4.0.4; ZTE Grand Era Build/IMM76L) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
   40 /    5 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:17.0) Gecko/20100101 Firefox/17.0
   40 /   19 /    0 - rogerbot/1.0 (http://www.seomoz.org/dp/rogerbot, rogerbot-crawler <EMAIL>)
   40 /    4 /    0 - Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; SP-A20i Build/MF_ICS_02.19) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
   40 /    0 /    0 - Mozilla/5.0 (compatible; YandexImageResizer/2.0;  http://yandex.com/bots)
   40 /    9 /    0 - Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; SonyEricssonLT26w Build/6.1.A.2.55) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
   40 /    1 /    1 - Opera/9.80 (Android; Opera Mini/7.5.33361/29.3530; U; en) Presto/2.8.119 Version/11.10
   40 /   13 /   13 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:5.0.1) Gecko/20100101 Firefox/5.0.1
   40 /    4 /    5 - Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0; MAAU)
   39 /   19 /    0 - Twitterbot/1.0
   38 /    2 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB7.4; chromeframe/26.0.1410.64; EasyBits GO v1.0; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
   37 /    4 /    0 - Opera/9.80 (Android; Opera Mini/7.0.29733/29.3271; U; en) Presto/2.8.119 Version/11.10
   37 /   16 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8) AppleWebKit/536.25 (KHTML, like Gecko) Version/6.0 Safari/536.25
   37 /    3 /    4 - Mozilla/5.0 (iPhone; CPU iPhone OS 5_1_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9B208 Safari/7534.48.3
   37 /   11 /   14 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/535.19 (KHTML, like Gecko) Ubuntu/10.04 Chromium/18.0.1025.168 Chrome/18.0.1025.168 Safari/535.19
   37 /    4 /    4 - Mozilla/5.0 (Linux; Android 4.0.4; ST26i Build/11.0.A.3.18) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19
   37 /    1 /    0 - Opera/9.80 (Android 2.3.4; Linux; Opera Mobi/ADR-1301071820) Presto/2.11.355 Version/12.10
   36 /   16 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:*******) Gecko/20091221 Firefox/3.5.7 (.NET CLR 3.5.30729)
   36 /    3 /    0 - Mozilla/5.0 (iPhone; CPU iPhone OS 5_1_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9B206 Safari/7534.48.3
   36 /    2 /    4 - Mozilla/5.0 (Linux; Android 4.1.2; GT-I9300 Build/JZO54K) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
   36 /    4 /    0 - Mozilla/5.0 (iPad; CPU OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B329
   35 /    6 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:********) Gecko/20110319 Firefox/3.6.16 sputnik ********
   35 /    3 /    4 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; Win64; x64; Trident/6.0; Touch; MASMJS)
   35 /    9 /    5 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.17 (KHTML, like Gecko) Ubuntu Chromium/24.0.1312.56 Chrome/24.0.1312.56 Safari/537.17
   35 /    4 /    7 - Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.56 Safari/537.36
   34 /    2 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_2) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.65 Safari/537.31
   34 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:6.0.2) Gecko/20100101 Firefox/6.0.2
   33 /    2 /    0 - Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:18.0) Gecko/20130119 Firefox/18.0
   33 /    3 /    0 - Mozilla/5.0 (iPad; CPU OS 6_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) CriOS/26.0.1410.50 Mobile/10B141 Safari/8536.25
   33 /    2 /    5 - Mozilla/5.0 (Linux; Android 4.0.3; HUAWEI MediaPad Build/HuaweiMediaPad) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Safari/537.31
   33 /    3 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; InfoPath.1; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
   32 /    4 /    0 - Mozilla/5.0 (Linux; Android 4.1.2; ST26i Build/11.2.A.0.21) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19
   32 /    4 /    6 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; InfoPath.1; InfoPath.2; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
   32 /    2 /    6 - Mozilla/5.0 (Android; Tablet; rv:20.0) Gecko/20.0 Firefox/20.0
   32 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; de; rv:*******) Gecko/********** Firefox/3.0.7 (via ggpht.com)
   32 /    3 /    5 - Mozilla/5.0 (Windows NT 6.1; rv:10.0.1) Gecko/20100101 Firefox/10.0.1
   32 /   26 /    0 - JS-Kit URL Resolver, http://js-kit.com/
   32 /    2 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1271.64 Safari/537.11
   32 /    1 /    0 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.11 (KHTML, like Gecko) Ubuntu Chromium/23.0.1271.97 Chrome/23.0.1271.97 Safari/537.11
   32 /   29 /    0 - Mozilla/5.0 (compatible; SISTRIX Crawler; http://crawler.sistrix.net/)
   32 /   27 /    0 - Mozilla/5.0 (compatible; TweetmemeBot/3.0;  http://tweetmeme.com/)
   32 /    7 /    0 - Opera/9.80 (Windows NT 6.1) Presto/2.12.388 Version/12.13
   31 /    5 /    5 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B145 [FBAN/FBIOS;FBAV/6.0.2;FBBV/183159;FBDV/iPhone4,1;FBMD/iPhone;FBSN/iPhone OS;FBSV/6.1.1;FBSS/2; FBCR/O2;FBID/phone;FBLC/en_US;FBOP/1]
   31 /    9 /    0 - Mozilla/5.0 (X11; Linux x86_64; rv:19.0) Gecko/20130403 Firefox/19.0
   31 /    3 /    0 - Mozilla/5.0 (iPad; CPU OS 6_1_2 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B146
   31 /    3 /    1 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_2 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B146 Safari/8536.25
   31 /    2 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
   30 /    2 /    5 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.2; Win64; x64; Trident/4.0; Avant Browser; .NET CLR 2.0.50727)
   30 /    1 /    0 - Mozilla/5.0 (iPad; CPU OS 6_1_2 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) CriOS/26.0.1410.50 Mobile/10B146 Safari/8536.25
   30 /    1 /    1 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 3.0.04506.648; .NET CLR 3.5.21022; OfficeLiveConnector.1.5; OfficeLivePatch.1.3; InfoPath.3; .NET4.0C; .NET4.0E; .NET CLR 3.0.04506.30; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
   30 /    4 /    3 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1271.97 Safari/537.11
   30 /    7 /    7 - Opera/9.80 (Windows NT 6.1; WOW64; Edition Yx) Presto/2.12.388 Version/12.14
   29 /    7 /    8 - Mozilla/5.0 (Linux; U; Android 4.0.3; ru-ru; EVO3D_X515m Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
   29 /    2 /    0 - Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:17.0) Gecko/20100101 Firefox/17.0
   29 /    4 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; U; ru) Presto/2.10.229 Version/11.64
   29 /    2 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/6.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.2; .NET4.0C; .NET4.0E)
   29 /    5 /    0 - Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; HTC_DesireS_S510e Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
   28 /    3 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB7.4; Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1) ; (R1 1.6); AskTbIMB/5.9.1.14019)
   28 /    2 /    3 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.97 Safari/537.22
   28 /    0 /    4 - Opera/9.80 (Windows NT 6.1; U; en) Presto/2.10.289 Version/12.01
   28 /   24 /    0 - Mozilla/5.0 (compatible; PaperLiBot/2.1; http://support.paper.li/entries/20023257-what-is-paper-li)
   28 /    3 /    2 - Mozilla/5.0 (Android; Tablet; rv:21.0) Gecko/21.0 Firefox/21.0
   28 /    2 /    0 - Mozilla/5.0 (Linux; Android 4.2.2; Galaxy Nexus Build/JDQ39) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.54 Mobile Safari/537.31
   28 /    2 /    0 - Mozilla/5.0 (iPod; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B329
   28 /    3 /    4 - Mozilla/5.0 (Mobile; rv:18.0) Gecko/18.0 Firefox/18.0
   28 /    1 /    1 - Mozilla/5.0 (Android; Mobile; rv:20.0) Gecko/20.0 Firefox/20.0
   27 /   21 /    1 - Opera/9.80 (Windows NT 5.1) Presto/2.12.388 Version/12.11
   27 /    2 /    0 - Opera/9.80 (Android; Opera Mini/6.5.29194/29.3271; U; ru) Presto/2.8.119 Version/11.10
   27 /    1 /    3 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 6.0 (build 5972)) Presto/2.12.388 Version/12.15
   27 /    2 /    0 - Opera/9.80 (Windows NT 6.0; U; ru) Presto/2.8.131 Version/11.10
   27 /    1 /    1 - Mozilla/5.0 (Windows; U; Windows NT 6.0; ru; rv:*******) Gecko/20100625 MRA 5.5 (build 02842) Firefox/3.6.6 ( .NET CLR 3.5.30729; .NET4.0C) sputnik *********
   27 /    0 /    0 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Win64; x64; Trident/6.0)
   27 /    3 /    5 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/534.30 (KHTML, like Gecko) Chrome/12.0.742.100 Safari/534.30
   27 /    1 /    0 - Mozilla/5.0 (Linux; Android 4.1.2; GT-I8190 Build/JZO54K) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
   26 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; AskTbFF/5.15.4.23821; MSOffice 12)
   26 /    2 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:1.9.2.12) Gecko/20101026 Firefox/3.6.12
   26 /    1 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_4) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.43 Safari/537.31
   26 /    4 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 6.1; WOW64; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.3; .NET4.0C; .NET4.0E; BRI/2)
   26 /   20 /    0 - Mozilla/5.0 (compatible; ZumBot/1.0; http://help.zum.com/inquiry)
   26 /    7 /    0 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/534.34 (KHTML, like Gecko) rekonq Safari/534.34
   25 /    2 /    2 - Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 920)
   25 /    3 /    3 - Opera/9.80 (J2ME/MIDP; Opera Mini/7.1.32052/29.3345; U; ru) Presto/2.8.119 Version/11.10
   25 /    6 /    0 - Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US; rv:1.9.2.28) Gecko/20120306 Firefox/3.6.28
   25 /    2 /    0 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.40 Safari/537.31
   25 /    2 /    2 - Mozilla/5.0 (Linux; U; Android 2.3.5; ru-ru; HTC_DesireHD_A9191 Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
   25 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB7.4; .NET CLR 1.1.4322)
   25 /    1 /    4 - Mozilla/5.0 (Linux; Android 4.0.4; ST25i Build/6.1.1.B.1.10) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
   25 /   12 /    2 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.04506.648; .NET CLR 3.5.21022; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; InfoPath.2)
   25 /    2 /    0 - Mozilla/5.0 (Linux; U; Android 2.3.6; ru-ru; Liquid MT Build/GRK39F) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
   25 /    2 /    2 - Mozilla/5.0 (Windows NT 6.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36
   25 /    4 /    0 - Mozilla/5.0 (Windows NT 6.1; rv:16.0) Gecko/20100101 Firefox/16.0
   24 /    3 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:23.0) Gecko/20130404 Firefox/23.0
   24 /    4 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10.7; rv:19.0) Gecko/20100101 Firefox/19.0
   24 /    0 /    0 - Wget/1.14 (linux-gnu)
   24 /   14 /    0 - Mozilla/5.0 (compatible; Baiduspider/2.0;  http://www.baidu.com/search/spider.html)
   24 /    1 /    1 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36
   24 /    2 /    0 - Opera/9.80 (Windows NT 6.2; WOW64) Presto/2.12.388 Version/12.14
   24 /    2 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.3; .NET4.0C; .NET4.0E; BRI/2)
   24 /    2 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:1.9.2.12) Gecko/20101026 Firefox/3.6.12 YB/5.1.3
   23 /    2 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/535.1 (KHTML, like Gecko) Chrome/13.0.782.112 Safari/535.1
   23 /    1 /    0 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0; Touch)
   23 /    3 /    3 - Mozilla/5.0 (Windows NT 6.2; WOW64; rv:21.0) Gecko/20100101 Firefox/21.0
   22 /    1 /    0 - Mozilla/5.0 (X11; Linux i686 (x86_64)) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1271.95 Safari/537.11
   22 /    2 /    0 - Opera/9.80 (Windows NT 6.1; U; ru) Presto/2.5.24 Version/10.53
   22 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Win64; x64; Trident/6.0; .NET CLR 2.0.50727; SLCC2; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.3; .NET4.0C; .NET4.0E; ms-office; MSOffice 14)
   22 /    1 /    1 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/6.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET4.0E)
   22 /    2 /    0 - Opera/9.80 (Windows NT 5.1; U; ru) Presto/2.2.15 Version/10.10
   22 /    3 /    4 - Mozilla/5.0 (Linux; U; Android 4.1.1; ru-ru; Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30
   22 /    2 /    2 - Mozilla/5.0 (X11; Linux x86_64; rv:20.0) Gecko/20130430 Firefox/20.0
   22 /    2 /    2 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.28 (KHTML, like Gecko) Chrome/26.0.1397.2 Safari/537.28
   22 /    6 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.1634 Safari/535.19 YE
   22 /   20 /    0 - MetaURI API/2.0  metauri.com
   21 /    1 /    2 - Mozilla/5.0 (Linux; Android 4.1.2; SAMSUNG-SGH-I727 Build/JZO54K) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Mobile Safari/537.22
   21 /    2 /    2 - Opera/9.80 (Android 4.1.2; Linux; Opera Mobi/ADR-1301071820) Presto/2.11.355 Version/12.10
   21 /    1 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1229.79 Safari/537.4
   21 /    1 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/535.11 (KHTML, like Gecko) Chrome/17.0.963.47 Safari/535.11 MRCHROME
   21 /   21 /    0 - Mozilla/3.0 (compatible; Indy Library)
   20 /    2 /    0 - Opera/9.80 (Windows NT 6.1; U; Edition Campaign 09; ru) Presto/2.9.168 Version/11.52
   20 /    1 /    1 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; SLCC1; .NET CLR 2.0.50727; Media Center PC 5.0; InfoPath.2; .NET CLR 3.0.30729)
   20 /    2 /    0 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B143 Safari/8536.25
   20 /    3 /    5 - Mozilla/5.0 (Linux; U; Android 4.1.2; ru-ru; SonyST26i Build/11.2.A.0.21) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
   20 /   10 /    0 - Microsoft Data Access Internet Publishing Provider Protocol Discovery
   20 /   14 /    0 - curl/7.15.5 (x86_64-redhat-linux-gnu) libcurl/7.15.5 OpenSSL/0.9.8b zlib/1.2.3 libidn/0.6.5
   20 /    0 /    1 - Mozilla/5.0 (compatible; MSIE 10.0; AOL 9.6; AOLBuild 4340.168; Windows NT 6.1; Trident/6.0)
   19 /    1 /    0 - Mozilla/5.0 (Linux; Android 4.2.2; Nexus 7 Build/JDQ39) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Safari/537.31
   19 /    1 /    0 - Mozilla/5.0 (X11; Ubuntu; Linux i686; rv:14.0) Gecko/20100101 Firefox/14.0.1
   19 /    3 /    0 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.4 (KHTML, like Gecko) Ubuntu/12.10 Chromium/22.0.1229.94 Chrome/22.0.1229.94 Safari/537.4
   19 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
   19 /    1 /    0 - Opera/9.80 (Windows NT 6.1; U; Edition Ukraine Local; ru) Presto/2.9.168 Version/11.52
   19 /    0 /    1 - Opera/9.80 (Windows NT 6.1; MRA 6.0 (build 6089)) Presto/2.12.388 Version/12.15
   19 /    0 /    0 - Mozilla/5.0 (compatible; special_archiver/3.1.1  http://www.archive.org/details/archive.org_bot)
   19 /    2 /    0 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0)
   19 /    0 /    1 - Opera/9.80 (Windows NT 5.1; Edition Rambler) Presto/2.12.388 Version/12.15
   19 /    0 /    1 - Opera/9.80 (Windows NT 6.1; U; ru) Presto/2.10.289 Version/12.00
   19 /    3 /    1 - Mozilla/5.0 (Windows NT 5.1; rv:12.0) Gecko/20100101 Firefox/12.0
   18 /    1 /    0 - Mozilla/5.0 (Windows NT 5.2; rv:17.0) Gecko/20100101 Firefox/17.0
   18 /    2 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; BTRS124829; Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1) ; InfoPath.2; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C; .NET4.0E)
   18 /    1 /    0 - Opera/9.80 (Android; Opera Mini/7.5.32193/29.3183; U; ru) Presto/2.8.119 Version/11.10
   18 /    3 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET CLR 1.1.4322; InfoPath.3; MS-RTC LM 8)
   18 /    2 /    2 - Mozilla/5.0 (Linux; Android 4.0.3; U9200 Build/HuaweiU9200) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19
   18 /    2 /    0 - Mozilla/5.0 (Windows NT 6.1; rv:9.0.1) Gecko/20100101 Firefox/9.0.1
   18 /    2 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; InfoPath.3)
   18 /   11 /    0 - Mozilla/5.0 (compatible
   18 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
   18 /    4 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C)
   18 /    1 /    1 - Mozilla/5.0 (Linux; U; Android 4.1.1; ru-ru; NEWMAN N1 Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
   18 /    9 /    0 - Mozilla/5.0 (compatible; YandexFavicons/1.0;  http://yandex.com/bots)
   17 /    1 /    0 - Opera/9.80 (Android; Opera Mini/7.5.32193/29.3271; U; ru) Presto/2.8.119 Version/11.10
   17 /    0 /    0 - Mozilla/5.0 (Linux; Android 4.2.1; Galaxy Nexus Build/JOP40D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19
   17 /    1 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_3) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.57 Safari/537.17
   17 /    1 /    2 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Iron/27.0.1500.0 Chrome/27.0.1500.0 Safari/537.36
   17 /    1 /    1 - Mozilla/5.0 (PlayBook; U; RIM Tablet OS 2.1.0; en-US) AppleWebKit/536.2  (KHTML, like Gecko) Version/7.2.1.0 Safari/536.2 
   17 /    1 /    0 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.142 Safari/535.19
   17 /    3 /    3 - Opera/9.80 (Android; Opera Mini/7.5.32193/29.3417; U; ru) Presto/2.8.119 Version/11.10
   17 /    2 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET4.0E; InfoPath.2)
   17 /    1 /    0 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.152 Safari/537.22
   17 /    1 /    0 - Mozilla/5.0 (Linux; U; Android 2.3.5; ru-ru; HTC_DesireS_S510e Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
   17 /    0 /    1 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/535.11 (KHTML, like Gecko) Chrome/17.0.963.46 Safari/535.11 MRCHROME
   17 /    0 /    1 - Mozilla/5.0 (X11; Linux i686; rv:10.0.11) Gecko/20100101 Firefox/10.0.11
   17 /    0 /    1 - Mozilla/5.0 (iPad; CPU OS 6_0_1 like Mac OS X; ru-ru) AppleWebKit/534.46.0 (KHTML, like Gecko) CriOS/21.0.1180.82 Mobile/10A523 Safari/7534.48.3
   16 /    1 /    1 - Opera/9.80 (Windows NT 5.2; WOW64) Presto/2.12.388 Version/12.15
   16 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:20.0) Gecko/20100101 Firefox/20.0 AlexaToolbar/alxf-2.17
   16 /    1 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1464.0 Safari/537.36
   16 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US) AppleWebKit/534.2 (KHTML, like Gecko) Chrome/6.0.447.0 Safari/534.2
   16 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; Tablet PC 2.0; LEN2)
   16 /    3 /    0 - Mozilla/5.0 (iPod; CPU iPhone OS 6_1_2 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B146
   16 /    1 /    0 - Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; GT-I8350)
   16 /    0 /    0 - AppEngine-Google; ( http://code.google.com/appengine; appid: s~pc-gizmos)
   16 /    1 /    3 - Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19
   16 /    0 /    1 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_5) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1229.94 Safari/537.4
   16 /    1 /    1 - Opera/9.80 (Windows NT 6.2) Presto/2.12.388 Version/12.15
   15 /    0 /    1 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.43 Safari/537.31 u01-04
   15 /    1 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:15.0) Gecko/20100101 Firefox/15.0.1
   15 /    0 /    1 - Mozilla/5.0 (Linux; U; Android 2.3.6; en-us; GT-I8150 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
   15 /    0 /    1 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:22.0) Gecko/20130416 Firefox/22.0
   15 /    3 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:10.0.5) Gecko/20100101 Firefox/10.0.5
   15 /    0 /    1 - Opera/9.80 (Android; Opera Mini/7.5.33286/29.3345; U; ru) Presto/2.8.119 Version/11.10
   15 /    1 /    0 - Mozilla/5.0 (Linux; U; Android 4.0.3; ru-ru; HTC Sensation Z710e Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
   15 /    1 /    0 - Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; IncredibleS_S710e Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
   15 /    1 /    1 - Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.20 Safari/537.36  OPR/15.0.1147.18 (Edition Next)
   15 /    1 /    0 - Mozilla/5.0 (Linux; U; Android 4.1.2; ru-ru; GT-I9070 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
   15 /    1 /    0 - Mozilla/5.0 (X11; FreeBSD amd64; rv:14.0) Gecko/20100101 Firefox/14.0.1
   15 /    0 /    0 - Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36
   15 /    1 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:7.0.1) Gecko/20100101 Firefox/7.0.1
   15 /    0 /    1 - Opera/9.80 (Windows NT 5.1; U; MRA 5.7 (build 03686); ru) Presto/2.10.289 Version/12.01
   15 /    1 /    1 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB7.4; InfoPath.1; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30; .NET CLR 3.0.04506.648; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C; .NET4.0E)
   15 /    1 /    1 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.20 Safari/537.36  OPR/15.0.1147.18 (Edition Next)
   15 /    0 /    1 - Opera/9.80 (Windows NT 5.1; MRA 6.0 (build 5970)) Presto/2.12.388 Version/12.15
   14 /    1 /    3 - Mozilla/5.0 (X11; Linux x86_64; rv:20.0) Gecko/20130413 Firefox/20.0
   14 /    2 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; InfoPath.2; .NET CLR 1.1.4322; MS-RTC LM 8)
   14 /    1 /    1 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.46 Safari/536.5 Nichrome/self/19
   14 /    1 /    0 - Mozilla/5.0 (Linux; Android 4.1.1; GT-N8000 Build/JRO03C) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Safari/537.31
   14 /    1 /    1 - Opera/9.80 (J2ME/MIDP; Opera Mini/4.1.13907/29.3345; U; ru) Presto/2.8.119 Version/11.10
   14 /    6 /    0 - Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:15.0) Gecko/20100101 Firefox/15.0.1
   14 /    1 /    0 - Mozilla/5.0 (Linux; U; Android 2.3.6; ru-ru; GT-S5830i Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
   14 /    1 /    1 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.2; Win64; x64; Trident/6.0; Touch; .NET4.0E; .NET4.0C; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET CLR 2.0.50727; Tablet PC 2.0; MASMJS)
   14 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; BTRS126674; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET CLR 1.1.4322)
   14 /   14 /    0 - W3C_Validator/1.3 http://validator.w3.org/services
   14 /    0 /    0 - Mozilla/5.0 (Linux; U; Android 2.3.6; ru-ru; GT-I9001 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
   14 /    2 /    2 - Opera/9.80 (Android; Opera Mini/7.5.33361/29.3594; U; ru) Presto/2.8.119 Version/11.10
   14 /    0 /    0 - Mozilla/5.0 (X11; Linux i686; rv:6.0) Gecko/20110328 Firefox/6.0.2
   14 /    1 /    1 - Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1485.0 Safari/537.36
   14 /    1 /    1 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.65 Safari/537.36
   14 /    2 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:1.8.1.1) Gecko/20061204 Firefox/2.0.0.1 WebMoney Advisor MRA 5.7 (build 03796);
   14 /    1 /    1 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; DefView; .NET4.0C; Tablet PC 2.0; ya.ru)
   14 /    1 /    1 - Opera/9.80 (Android; Opera Mini/7.5.33361/29.3345; U; en) Presto/2.8.119 Version/11.10
   13 /    1 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_5) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.57 Safari/537.17
   13 /    1 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:2.0) Gecko/20100101 Firefox/4.0
   13 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET CLR 1.1.4322; InfoPath.2; .NET4.0E; MS-RTC LM 8)
   13 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C; .NET4.0E)
   13 /    1 /    1 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.70 Safari/537.17 YE
   13 /    1 /    1 - Mozilla/5.0 (Linux; Android 4.1.1; HTC Desire X Build/JRO03C) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
   13 /    2 /    1 - Opera/9.80 (Android 2.3.6; Linux; Opera Mobi/ADR-1301071820) Presto/2.11.355 Version/12.10
   13 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; .NET CLR 1.1.4322; InfoPath.1; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30; Dealio Deskball 3.0)
   13 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; Trident/4.0; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
   13 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.3; DefView)
   13 /    2 /    0 - Mozilla/5.0 (Linux; Android 4.2.2; Nexus 7 Build/JDQ39) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19
   13 /    1 /    0 - Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1478.0 Safari/537.36
   13 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30)
   12 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; Trident/4.0; BTRS129253; MRSPUTNIK 2, 4, 1, 105; MRA 8.0 (build 6017); SIMBAR={FB3C092F-6650-11E2-B314-00197D7E8F2A}; Avant Browser; .NET CLR 1.1.4322; .NET CLR 2.0.50727; Avant Browser)
   12 /    0 /    0 - Opera/9.80 (Android; Opera Mini/7.5.32193/29.3134; U; ru) Presto/2.8.119 Version/11.10
   12 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1271.64 Safari/537.11
   12 /    1 /    0 - Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1464.0 Safari/537.36
   12 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.152 Safari/537.22
   12 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/536.11 (KHTML, like Gecko) Chrome/20.0.1132.57 Safari/536.11
   12 /    2 /    0 - Mozilla/5.0 (Windows NT 6.0; rv:13.0) Gecko/20100101 Firefox/13.0.1
   12 /    0 /    0 - Mozilla/5.0 (compatible) Feedfetcher-Google;( http://www.google.com/feedfetcher.html)
   12 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
   12 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 2.0.50727)
   12 /    1 /    0 - Mozilla/5.0 (X11; Linux x86_64; rv:23.0) Gecko/20130405 Firefox/23.0
   12 /    1 /    0 - Mozilla/5.0 (Linux; U; Android 2.3.7; ru-ru; E15i Build/3.0.1.A.0.145; MiniCM7-2.2.1) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
   12 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; InfoPath.2)
   12 /    9 /    0 - Mozilla/5.0 (X11; Linux i686; rv:6.0) Gecko/20100101 Firefox/6.0
   12 /    1 /    0 - Opera/9.80 (Windows NT 6.1; U; ru) Presto/2.5.22 Version/10.50
   12 /    1 /    0 - Opera/9.80 (Android 2.3.3; Linux; Opera Mobi/ADR-1210091050) Presto/2.11.355 Version/12.10
   12 /    1 /    1 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.8 (KHTML, like Gecko) Chrome/23.0.1250.0 Safari/537.8
   12 /    0 /    0 - Mozilla/5.0 (Linux; U; Android 4.1.1; ru-ru; HTC_One_S Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
   11 /    1 /    1 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.73 Safari/537.36
   11 /    1 /    1 - Mozilla/5.0 (Windows NT 5.2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36
   11 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:*******) Gecko/2008070208
   11 /    1 /    0 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B145
   11 /    1 /    0 - Mozilla/5.0 (iPad; CPU OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10A523
   11 /    1 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:********) Gecko/2010031422 Firefox/3.0.19
   11 /   11 /    0 - Mozilla/4.0 (compatible; MSIE 5.01; Windows NT 5.0)
   11 /    1 /    1 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:13.0) Gecko/20100101 Firefox/13.0.1
   11 /    1 /    1 - Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 710)
   11 /    0 /    0 - facebookplatform/1.0 ( http://developers.facebook.com)
   11 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB6; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
   11 /    1 /    0 - Mozilla/5.0 (Windows NT 6.0) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 Safari/537.22
   11 /    0 /    0 - Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_2_1 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Mobile/8C148
   11 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; InfoPath.2; .NET CLR 1.1.4322; MS-RTC LM 8; .NET4.0E)
   11 /    1 /    0 - Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; GT-P5100 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30
   11 /    1 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/535.1 (KHTML, like Gecko) Chrome/13.0.782.215 Safari/535.1
   10 /    1 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:********) Gecko/20101026 Firefox/3.5.15
   10 /    4 /    0 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.1 (KHTML, like Gecko) Ubuntu/11.10 Chromium/14.0.835.202 Chrome/14.0.835.202 Safari/535.1
   10 /    2 /    0 - Mozilla/4.0 (compatible; MSIE 5.0; Windows 3.1; TUCOWS Network)
   10 /    8 /    0 - Kimengi/nineconnections.com
   10 /    0 /    0 - AppEngine-Google; ( http://code.google.com/appengine; appid: s~testpzgizmo)
   10 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.3; .NET4.0C; .NET4.0E)
    9 /    0 /    0 - Opera/9.80 (Windows NT 6.1; U; ru) Presto/2.8.131 Version/11.10
    9 /    5 /    0 - Mozilla/5.0 (iPad; CPU OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) CriOS/26.0.1410.50 Mobile/10B329 Safari/8536.25
    9 /    3 /    0 - Mozilla/5.0 (compatible; AhrefsBot/4.0;  http://ahrefs.com/robot/)
    9 /    1 /    0 - Mozilla/5.0 (Linux; U; Android 4.0.4; en-ru; IncredibleS_S710e Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
    9 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET4.0E; MS-RTC LM 8; AskTbFXTV5/5.15.15.35882; MSOffice 12)
    9 /    4 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:13.0) Gecko/20100101 Firefox/13.0
    9 /    1 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.47 Safari/537.36
    9 /    1 /    0 - Mozilla/5.0 (X11; Linux x86_64; rv:10.0.6) Gecko/20100101 Firefox/10.0.6 Iceweasel/10.0.6
    9 /    7 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; U; en) Presto/2.10.289 Version/12.01
    8 /    0 /    0 - Mozilla/4.0 (compatible;)
    8 /    1 /    0 - Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/533.3 (KHTML, like Gecko) Qt/4.7.1 Safari/533.3
    8 /    8 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; de; rv:1.9) Gecko/2008052906 Firefox/3.0
    8 /    0 /    0 - Mozilla/5.0 (X11; Linux x86_64) KHTML/4.10.1 (like Gecko) Konqueror/4.10
    8 /    0 /    0 - MobileSafari/8536.25 CFNetwork/609 Darwin/13.0.0
    8 /    6 /    0 - Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0; chromeframe/26.0.1410.64)
    7 /    0 /    0 - Microsoft-WebDAV-MiniRedir/6.2.9200
    7 /    0 /    0 - Mozilla/5.0 (compatible; bingbot/2.0;  http://www.bing.com/bingbot.htm) SitemapProbe
    7 /    0 /    0 - Sogou web spider/4.0( http://www.sogou.com/docs/help/webmasters.htm#07)
    7 /    3 /    0 - Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:11.0) Gecko/20120313 Firefox/11.0
    7 /    3 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:17.0) Gecko/17.0 Firefox/17.0
    7 /    0 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; Edition Next) Presto/2.12.388 Version/12.12
    6 /    0 /    0 - Mozilla/5.0 (Windows NT 6.2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36
    6 /    0 /    0 - Mozilla/5.0 (X11; Linux x86_64; rv:20.0) Gecko/20130512 Firefox/20.0
    6 /    0 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_2) AppleWebKit/536.26.17 (KHTML, like Gecko) Version/6.0.2 Safari/536.26.17
    6 /    0 /    0 - Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.14 (KHTML, like Gecko) Chrome/24.0.1292.0 Safari/537.14
    6 /    0 /    0 - Mozilla/5.0 (compatible; Google Desktop/5.9.1005.12335; http://desktop.google.com/)
    6 /    6 /    0 - Opera/9.80 (Windows NT 6.1; Edition Yx) Presto/2.12.388 Version/12.10
    6 /    5 /    0 - Opera/9.80 (Windows NT 6.1; WOW64) Presto/2.12.388 Version/12.10
    6 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Win64; x64; Trident/6.0; .NET CLR 2.0.50727; SLCC2; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET4.0C; AlexaToolbar/amzni-3.0; MALN; ms-office; MSOffice 14)
    6 /    3 /    0 - peerindex
    6 /    0 /    0 - Mozilla/5.001 (windows; U; NT4.0; en-us) Gecko/25250101
    6 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:6.0.1) Gecko/20100101 Firefox/6.0.1
    5 /    0 /    0 - Mozilla/3.0 (OS/2; U)
    5 /    3 /    0 - msnbot-NewsBlogs/2.0b ( http://search.msn.com/msnbot.htm)
    5 /    0 /    0 - Opera/9.80 (Windows NT 5.1; U; ru) Presto/2.10.229 Version/11.61
    5 /    3 /    0 - Opera/9.80 (Android 2.3.5; Linux; Opera Mobi/ADR-1210241511) Presto/2.11.355 Version/12.10
    5 /    1 /    0 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/536.11 (KHTML, like Gecko) Chrome/20.0.1132.57 Safari/536.11
    5 /    2 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1229.79 Safari/537.4 u01-09
    5 /    5 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1271.95 Safari/537.11
    5 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.56 Safari/537.17
    5 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET4.0E)
    5 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.2; Trident/4.0; .NET CLR 1.1.4322; InfoPath.2)
    5 /    2 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; U; ru) Presto/2.10.289 Version/12.00
    4 /    0 /    0 - Mozilla/5.0 (Linux; Android 4.1.1; A701 Build/JRO03H) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19
    4 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1518.0 Safari/537.36
    4 /    2 /    0 - Opera/9.80 (X11; Linux x86_64; U; en) Presto/2.10.289 Version/12.00
    4 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.2; MSOffice 12)
    4 /    1 /    1 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1;  SLCC1;  .NET CLR 1.1.4322;  .NET CLR 2.0.50727;  .NET CLR 3.0.04506.648)
    4 /    0 /    0 - MSFrontPage/12.0
    4 /    0 /    0 - Mozilla/4.61 (Macintosh; I; PPC)
    4 /    0 /    0 - Mozilla/4.0 (compatible; MS FrontPage 12.0)
    4 /    1 /    1 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1;  SV1;  .NET CLR 1.1.4322;  .NET CLR 2.0.50727;  .NET CLR 3.0.04506.648)
    4 /    0 /    0 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.2 (KHTML, like Gecko) Chrome/15.0.874.102 Safari/535.2
    4 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)
    4 /    4 /    0 - DavClnt
    4 /    1 /    1 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.2;  SV1;  .NET CLR 1.1.4322;  .NET CLR 2.0.40607;  .NET CLR 3.0.04506.648)
    4 /    0 /    0 - Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.69 Safari/537.17
    4 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET CLR 1.1.4322; .NET4.0C; .NET4.0E)
    4 /    1 /    1 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1;  SV1;  .NET CLR 1.1.4322;  .NET CLR 2.0.40607;  .NET CLR 3.0.04506.648)
    4 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; FunWebProducts; .NET CLR 1.1.4322; PeoplePal 6.2)
    4 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; Trident/4.0; 1A820D29-8445-92BE-2384-712BBCEDFDB1; MRSPUTNIK 2, 4, 1, 12; BTRS124447; GTB7.4; InfoPath.2; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET CLR 2.0.50727; AskTbFXTV5/5.8.0.12304; MSOffice 12)
    4 /    0 /    0 - Opera/9.80 (Android; Opera Mini/7.5.32200/29.3183; U; en) Presto/2.8.119 Version/11.10
    4 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; GTB6.6; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
    4 /    4 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/535.1 (KHTML, like Gecko) Chrome/13.0.782.215 Safari/535.1
    4 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.64 Safari/537.31 AlexaToolbar/alxg-3.1
    4 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET4.0C; AlexaToolbar/amzni-3.0; MALN; MSOffice 12)
    4 /    1 /    1 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.2;  SLCC1;  .NET CLR 1.1.4322;  .NET CLR 2.0.40607;  .NET CLR 3.0.04506.648)
    4 /    3 /    0 - Mozilla/5.0 (compatible; Exabot/3.0 (BiggerBetter);  http://www.exabot.com/go/robot)
    4 /    1 /    1 - Mozilla/5.0 (Linux; U; Android 2.3.3; ru-ru; LG-P500 Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 MMS/LG-Android-MMS-V1.0/1.2
    4 /    0 /    0 - Opera/9.80 (Android; Opera Mini/7.5.32200/29.3271; U; en) Presto/2.8.119 Version/11.10
    4 /    0 /    0 - Opera/9.80 (Windows NT 6.1; MRA 5.8 (build 4664)) Presto/2.12.388 Version/12.10
    4 /    0 /    0 - MobileSafari/8536.25 CFNetwork/609.1.4 Darwin/13.0.0
    4 /    0 /    0 - Sogou web spider/4.0
    4 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; InfoPath.1; .NET4.0C)
    3 /    2 /    0 - Mozilla/5.0 (Windows NT 6.1; rv:5.0) Gecko/20100101 Firefox/5.02
    3 /    1 /    0 - Mozilla/5.0 (compatible; archive.org_bot  http://www.archive.org/details/archive.org_bot)
    3 /    2 /    0 - Opera/9.80 (Windows NT 6.1) Presto/2.12.388 Version/12.11
    3 /    0 /    0 - Mozilla/5.0 (iPad; CPU OS 5_0_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9A405 Safari/7534.48.3
    3 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.04506.648; .NET CLR 3.5.21022; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C)
    3 /    0 /    0 - Googlebot-Video/1.0
    3 /    1 /    0 - Xenu Link Sleuth/1.3.8
    3 /    0 /    0 - Opera/9.80 (Windows NT 5.1; MRA 6.0 (build 6089)) Presto/2.12.388 Version/12.15
    3 /    0 /    0 - WordPress/3.5.1; http://peterpen-ctf.net
    3 /    2 /    0 - Lynx/2.8.8dev.15 libwww-FM/2.14 SSL-MM/1.4.1 GNUTLS/2.12.20
    3 /    3 /    0 - Java/1.6.0_26
    3 /    0 /    0 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/6.0
    3 /    0 /    0 - Xenu Link Sleuth/1.3.9 beta
    3 /    0 /    0 - Opera/9.80 (Series 60; Opera Mini/7.0.29482/29.3345; U; ru) Presto/2.8.119 Version/11.10
    3 /    0 /    0 - WordPress/3.5.1; https://peterpen-ctf.net
    3 /    2 /    0 - Opera/9.80 (Windows NT 5.1; Edition Yx) Presto/2.12.388 Version/12.11
    3 /    0 /    0 - URLGrabber
    3 /    0 /    0 - Mozilla/5.0 (Linux; U; Android 2.2.1; ru-ru; HTC_Wildfire_A3333 Build/FRG83D) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
    3 /    0 /    0 - Opera/9.80 (Windows NT 5.1; MRA 6.0 (build 5711)) Presto/2.12.388 Version/12.15
    3 /    0 /    0 - Microsoft Office Word 2013
    3 /    2 /    0 - Mozilla/5.0 (Windows NT 6.2; rv:16.0) Gecko/20100101 Firefox/16.0
    3 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; Avant Browser)
    3 /    0 /    0 - AndroidDownloadManager
    3 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
    3 /    0 /    0 - Mozilla/5.0 (Linux; U; Android 4.0.3; ru-ru; A500 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30
    3 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; rv:11.0) Gecko/20100101 Firefox/11.0
    3 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.3; .NET4.0C; .NET4.0E)
    3 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; SV1; .NET CLR 2.0.50727)
    3 /    0 /    0 - GFGET/0.1
    3 /    0 /    0 - Wget/1.10.1
    3 /    0 /    0 - Opera/9.80 (Macintosh; Intel Mac OS X 10.6.8; U; Edition MacAppStore; ru) Presto/2.9.168 Version/11.52
    3 /    0 /    0 - Opera/9.80 (Windows NT 6.2; Win64; x64) Presto/2.12.388 Version/12.15
    3 /    2 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/535.12 (KHTML, like Gecko) Maxthon/3.0 Chrome/18.0.966.0 Safari/535.12
    3 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; MRSPUTNIK 2, 4, 0, 508; GTB7.4; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0)
    3 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; ms-office; MSOffice 14)
    3 /    0 /    0 - ia_archiver(OS-Wayback)
    2 /    1 /    0 - Links (2.7; Linux 3.8.0-19-generic i686; GNU C 4.7.1; text)
    2 /    2 /    0 - Mozilla/5.0 (compatible; YandexBot/3.0; MirrorDetector;  http://yandex.com/bots)
    2 /    0 /    0 - Mozilla/5.0 (en-us) AppleWebKit/534.14 (KHTML, like Gecko; Google Wireless Transcoder) Chrome/9.0.597 Safari/534.14
    2 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; MSIE 5.5; Windows 95) Opera 7.03 [de]
    2 /    0 /    0 - Mozilla/5.0 (Android; Tablet; rv:17.0) Gecko/17.0 Firefox/17.0
    2 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Win64; x64; Trident/6.0; .NET CLR 2.0.50727; SLCC2; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET4.0C; AlexaToolbar/amzni-3.0; MALN)
    2 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; Trident/5.0; SLCC1; .NET CLR 2.0.50727; Media Center PC 5.0; InfoPath.2; .NET CLR 3.5.21022; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET4.0C; .NET4.0E; Tablet PC 2.0)
    2 /    0 /    0 - Opera/9.80 (Windows NT 5.1; Edition Yx) Presto/2.12.388 Version/12.15
    2 /    0 /    1 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1)
    2 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:2.0.1) Gecko/20100101 Firefox/4.0.1
    2 /    0 /    0 - Mozilla/5.0 (Linux; Android 4.0.4; MT27i Build/6.1.1.B.1.54) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Mobile Safari/537.22
    2 /    0 /    0 - Opera/9.80 (Android; Opera Mini/7.5.32200/29.3134; U; en) Presto/2.8.119 Version/11.10
    2 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.57 Safari/537.17
    2 /    2 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_2) AppleWebKit/534.52.7 (KHTML, like Gecko) Version/5.1.2 Safari/534.52.7
    2 /    0 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 5.8 (build 4133)) Presto/2.12.388 Version/12.15
    2 /    0 /    0 - User-Agent: Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
    2 /    0 /    0 - Mozilla/5.0 (X11; Linux x86_64; rv:20.0) Gecko/20130507 Firefox/20.0
    2 /    2 /    0 - Mozilla/5.0 (compatible; NetcraftSurveyAgent/1.0;  <EMAIL>)
    2 /    1 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/21.0.1180.89 Safari/537.1
    2 /    1 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 6.0 (build 5976)) Presto/2.12.388 Version/12.11
    2 /    1 /    0 - DoCoMo/2.0 P900i(c100;TB;W24H11) (compatible; ichiro/mobile goo; http://search.goo.ne.jp/option/use/sub4/sub4-1/)
    2 /    0 /    0 - Mozilla/5.0 (X11; Ubuntu; Linux i686; rv:16.0) Gecko/20100101 Firefox/16.0
    2 /    1 /    0 - Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1271.91 Safari/537.11
    2 /    1 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:16.0) Gecko/20100101 Firefox/16.0
    2 /    1 /    0 - Opera/9.80 (Windows NT 6.1; MRA 6.0 (build 5976)) Presto/2.12.388 Version/12.10
    2 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.52 Safari/536.5
    2 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; MRA 5.5 (build 02842); InfoPath.2; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
    2 /    1 /    1 - Opera/9.80 (X11; Linux x86_64; Edition Next) Presto/2.12.388 Version/12.15
    2 /    2 /    0 - Java/1.6.0_35
    2 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:16.0) Gecko/20100101 Firefox/16.0 05/02/2013 3:20:45 AM
    2 /    0 /    0 - Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; PAP4040_DUO Build/PrestigioPAP4040DUO) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
    2 /    1 /    0 - Opera/9.80 (Windows NT 6.1; Win64; x64) Presto/2.12.388 Version/12.11
    2 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/6.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET CLR 1.1.4322; InfoPath.3; .NET4.0E; MS-RTC LM 8; Microsoft Outlook 14.0.6025; ms-office; MSOffice 14)
    2 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.8.0.11)  Firefox/1.5.0.11; 360Spider
    2 /    0 /    0 - Mozilla/5.0 (X11; Linux i686; rv:20.0) Gecko/20100101 Firefox/20.0 TinEye/1.0 (via http://www.tineye.com/)
    2 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.2; Win64; x64; Trident/4.0; Avant Browser; .NET CLR 2.0.50727)
    2 /    0 /    0 - Opera/9.80 (Android; Opera Mini/7.5.32195/29.3417; U; ru) Presto/2.8.119 Version/11.10
    2 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; BTRS129253; InfoPath.2; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30; .NET4.0C; .NET4.0E; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
    2 /    1 /    0 - Mozilla/5.0
    2 /    1 /    0 - Mozilla/5.0 (Windows NT 6.0; rv:16.0) Gecko/20100101 Firefox/16.0
    2 /    2 /    0 - Mozilla/4.0 (compatible; Netcraft Web Server Survey)
    2 /    0 /    0 - Opera/9.80 (Windows NT 5.1; Edition Yx) Presto/2.12.388 Version/12.14
    2 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; MANM)
    2 /    0 /    0 - Opera/9.80 (Android; Opera Mini/7.5.32195/29.3638; U; ru) Presto/2.8.119 Version/11.10
    2 /    1 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.5 (KHTML, like Gecko) YaBrowser/1.1.1084.5409 Chrome/19.1.1084.5409 Safari/536.5
    2 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.46 Safari/536.5 Nichrome/self/19
    2 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB7.4; .NET CLR 2.0.50727; InfoPath.2; .NET CLR 1.1.4322; .NET CLR 3.0.04506.30; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
    2 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
    2 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; MRSPUTNIK 2, 4, 1, 209; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C)
    2 /    0 /    0 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A525 Safari/8536.25
    2 /    0 /    0 - Opera/9.80 (Android; Opera Mini/7.5.32195/29.3134; U; ru) Presto/2.8.119 Version/11.10
    2 /    0 /    0 - Opera/9.80 (Android; Opera Mini/6.5.29194/29.3507; U; ru) Presto/2.8.119 Version/11.10
    2 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; rv:1.7.3) Gecko/20041001 Firefox/0.10.1
    2 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; eSobiSubscriber ********; BRI/2; MAAR; .NET4.0C)
    2 /    2 /    0 - Crowsnest/0.5 ( http://www.crowsnest.tv/)
    2 /    1 /    0 - Opera/9.80 (Windows NT 6.0) Presto/2.12.388 Version/12.10
    2 /    0 /    0 - Mozilla/5.0 (Linux; Android 4.1.2; GT-N8000 Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.90 Safari/537.36
    2 /    1 /    0 - Mozilla/5.0 (X11; Linux x86_64; rv:18.0) Gecko/20100101 Firefox/18.0
    2 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET4.0E; InfoPath.3)
    2 /    0 /    0 - Opera/9.80 (Windows NT 5.1; U; ru) Presto/2.10.289 Version/12.01
    2 /    0 /    0 - Opera/9.80 (Windows NT 6.1; Edition Ukraine Local) Presto/2.12.388 Version/12.12
    2 /    0 /    0 - Mozilla/5.0 (iPad; U; CPU OS 4_3_5 like Mac OS X; ru-ru) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8L1 Safari/6533.18.5
    2 /    1 /    0 - netEstate NE Crawler ( http://www.website-datenbank.de/)
    2 /    1 /    0 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1312.57 Safari/537.17
    2 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; de; rv:*******; Google-SearchByImage) Gecko/********** Firefox/3.0.7
    2 /    0 /    0 - Opera/9.80 (Android; Opera Mini/7.5.32200/29.3345; U; en) Presto/2.8.119 Version/11.10
    2 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1485.0 Safari/537.36
    2 /    1 /    0 - Links (2.7; Linux 3.8.8 x86_64; GNU C 4.7.1; text)
    2 /    1 /    0 - SurcentroBot
    2 /    0 /    0 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0; MANMJS)
    2 /    2 /    0 - Mozilla/5.0 (compatible; Firefox Addon; Windows XP 5.1)
    2 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; MRSPUTNIK 2, 4, 0, 516; GTB7.4; MRA 5.10 (build 5310); .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30; InfoPath.1; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
    2 /    0 /    0 - Mozilla/5.0 (Java; U; ru; nokiac5-00) AppleWebKit/530.13 (KHTML, like Gecko) UCBrowser/8.6.0.199/69/444/UCWEB Mobile UNTRUSTED/1.0 3gpp-gba
    2 /    0 /    0 - Opera/9.80 (Android; Opera Mini/7.0.29530/29.3709; U; ru) Presto/2.8.119 Version/11.10
    2 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; rv:2.0b7pre) Gecko/20100921 Firefox/4.0b7pre
    2 /    0 /    0 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; Win64; x64; Trident/6.0; MASMJS)
    2 /    2 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:*******0) Gecko/2009042316 Firefox/3.0.10
    2 /    0 /    0 - Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0; NP06)
    2 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 1.0.3705; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30; .NET CLR 3.0.04506.648; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C; .NET4.0E; InfoPath.3)
    2 /    1 /    0 - Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US; rv:1.9.2.12) Gecko/20101026 Firefox/3.6.12
    2 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:10.0.2) Gecko/20100101 Firefox/10.0.2
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0; GTB6.6; SLCC1; .NET CLR 2.0.50727; Media Center PC 5.0; .NET CLR 3.0.30618; OfficeLiveConnector.1.5; OfficeLivePatch.1.3; .NET4.0C)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.0) AppleWebKit/534.30 (KHTML, like Gecko) Chrome/12.0.742.122 Safari/534.30 Nichrome/self/12
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; rv:20.0) Gecko/20100101 Firefox/20.0 IceDragon/20.0.1.14
    1 /    0 /    0 - Opera/9.80 (Android; Opera Mini/7.0.29952/29.3271; U; ru) Presto/2.8.119 Version/11.10
    1 /    1 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1229.96 Safari/537.4
    1 /    1 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/535.1 (KHTML, like Gecko) Chrome/14.0.835.163 Safari/535.1
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1) Presto/2.12.388 Version/12.13
    1 /    1 /    0 - SEO Browser
    1 /    0 /    0 - CodeGator Crawler v1.0
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C; .NET4.0E)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:7.0) Gecko/20100101 Firefox/7.0
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 2.0.50727; InfoPath.1)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1229.94 Safari/537.4
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; TencentTraveler)
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; MRA 6.0 (build 6068)) Presto/2.12.388 Version/12.15
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1; MRA 6.0 (build 5972)) Presto/2.12.388 Version/12.15
    1 /    0 /    0 - Opera/9.80 (X11; Linux zbov) Presto/2.11.355 Version/12.10
    1 /    1 /    0 - Microsoft Office Mobile /14.0
    1 /    1 /    0 - Mozilla/5.0 (Windows NT 6.1; rv:17.0) Gecko/17.0 Firefox/17.0
    1 /    1 /    0 - Opera/9.80 (Windows NT 5.1; U; YB/3.5.1; ru) Presto/2.10.229 Version/11.64
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; InfoPath.1; .NET CLR 1.1.4322; .NET CLR 2.0.50727)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/534.24 (KHTML, like Gecko) Chrome/11.0.696.65 Safari/534.24
    1 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 6.1; ru; rv:********) Gecko/2010031422 Firefox/3.0.19
    1 /    0 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36
    1 /    0 /    0 - Opera/9.80 (J2ME/MIDP; Opera Mini/7.1.32052/29.3417; U; ru) Presto/2.8.119 Version/11.10
    1 /    0 /    0 - Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0; BOIE9;ENUS)
    1 /    1 /    0 - Opera/9.80 (Windows NT 6.2; WOW64) Presto/2.12.388 Version/12.11
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; U; MRA 5.8 (build 4664); ru) Presto/2.10.229 Version/11.60
    1 /    1 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:8.0) Gecko/20100101 Firefox/8.0
    1 /    1 /    0 - Opera/9.80 (Windows NT 6.1; MRA 6.0 (build 5970)) Presto/2.12.388 Version/12.11
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 6.0 (build 5704)) Presto/2.12.388 Version/12.11
    1 /    1 /    0 - Mozilla/5.0 (X11; U; Linux i686; ru; rv:1.9.1) Gecko/20090630 Fedora/3.5-1.fc11 Firefox/3.5 GTB5
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 999.1; Unknown)
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Win64; x64; Trident/6.0; .NET CLR 2.0.50727; SLCC2; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET4.0C; AlexaToolbar/amzni-3.0; MALN; ms-office)
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; TencentTraveler ; Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1) ; .NET CLR 2.0.50727)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/535.2 (KHTML, like Gecko) Chrome/15.0.874.120 Safari/535.2
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; MRA 8.0 (build 6008)) Presto/2.12.388 Version/12.14
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; TencentTraveler ; Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1) ; .NET CLR 2.0.50727 ; .NET CLR 4.0.30319)
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1; MRA 5.7 (build 03789)) Presto/2.12.388 Version/12.15
    1 /    0 /    0 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0; Touch; MASMJS)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/536.11 (KHTML, like Gecko) Chrome/20.0.1132.47 Safari/536.11
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; Nevz Group Policy option; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET4.0C; Nevz Group Policy option)
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; Edition Yx) Presto/2.12.388 Version/12.10
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET4.0E; MS-RTC LM 8; ms-office; MSOffice 14)
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; U; Edition Next; Edition Yx; ru) Presto/2.11.310 Version/12.50
    1 /    1 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:16.0) Gecko/20121026 Firefox/16.0 SeaMonkey/2.13.2
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:20.0) Gecko/20100101 Firefox/20.0 TinEye/1.0 (via http://www.tineye.com/)
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; {E3AFBC29-379F-6981-3477-F67AEAD253D3}; .NET CLR 1.1.4322; .NET CLR 2.0.50727; OfficeLiveConnector.1.3; OfficeLivePatch.0.0)
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; DepositFiles/FileManager 0.9.9.206 YB/5.0.3) Presto/2.12.388 Version/12.14
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; MRSPUTNIK 2, 2, 0, 86; MRIE8PACK 2.0.1)
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1; U; ru) Presto/2.10.229 Version/11.60
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; U; ru) Presto/2.9.168 Version/11.51
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; MRA 6.0 (build 5972)) Presto/2.12.388 Version/12.10
    1 /    0 /    0 - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1271.64 Safari/537.11
    1 /    1 /    0 - Jakarta Commons-HttpClient/3.1
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB7.4; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30; .NET CLR 3.0.04506.648; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; InfoPath.2; .NET4.0C; .NET4.0E)
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0; Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1) ; .NET CLR 3.5.30729)
    1 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US) AppleWebKit/534.16 (KHTML, like Gecko) Chrome/10.0.648.204 Safari/534.16
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.2; WOW64; MRA 8.0 (build 5784)) Presto/2.12.388 Version/12.11
    1 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; MRA 4.9 (build 01863); MRSPUTNIK 2, 4, 1, 162)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/535.2 (KHTML, like Gecko) Chrome/15.0.874.120 Safari/535.2
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET4.0C; .NET4.0E; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
    1 /    0 /    0 - Opera/9.80 (X11; Linux i686; U; ru) Presto/2.7.62 Version/11.01
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1; U; ru) Presto/2.5.24 Version/10.53
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/6.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET CLR 1.1.4322; InfoPath.3; .NET4.0E; ms-office; MSOffice 14)
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1; U; ru) Presto/2.9.168 Version/11.50
    1 /    0 /    0 - Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_5_7; ru-ru) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Safari/530.17
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.1 (KHTML, like Gecko) Maxthon/3.0 Chrome/22.0.1229.79 Safari/537.1
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/536.5 (KHTML, like Gecko) YaBrowser/1.1.1084.5409 Chrome/19.1.1084.5409 Safari/536.5
    1 /    1 /    0 - Opera/9.80 (Windows NT 5.1; U; Edition Yx; ru) Presto/2.10.289 Version/12.02
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1.0 Safari/537.11
    1 /    1 /    0 - MFE_expand/0.1
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/534.24 (KHTML, like Gecko) Chrome/11.0.696.60 Safari/534.24
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; InfoPath.1)
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET4.0E)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:9.0) Gecko/20100101 Firefox/9.0
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1271.64 Safari/537.11
    1 /    0 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_6_8) AppleWebKit/534.57.2 (KHTML, like Gecko) Version/5.1.7 Safari/534.57.2
    1 /    1 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:********) Gecko/20080311 Firefox/********
    1 /    0 /    0 - Mozilla/5.0 (iPad; U; CPU OS 4_3_3 like Mac OS X; ru-ru) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8J2 Safari/6533.18.5
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1; MRA 6.0 (build 5998)) Presto/2.12.388 Version/12.11
    1 /    0 /    0 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0; MASEJS)
    1 /    0 /    0 - Linguee Bot (http://www.linguee.com/bot; <EMAIL>)
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1; U; ru) Presto/2.10.289 Version/12.00
    1 /    0 /    0 - Mozilla/5.0 (X11; Linux i686; rv:17.0) Gecko/20100101 Firefox/17.0
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1; U; MRA 5.9 (build 4953); ru) Presto/2.10.229 Version/11.60
    1 /    0 /    0 - HttpComponents/1.1
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:16.0) Gecko/20100101 Firefox/16.0 05/02/2013 3:20:44 AM
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Win64; x64; Trident/6.0; .NET CLR 2.0.50727; SLCC2; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET4.0E; InfoPath.3; ms-office)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.2) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.64 Safari/537.31
    1 /    0 /    0 - Mozilla/5.0 (Linux; U; Android 4.1.2; ru-ru; GT-I9100 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
    1 /    1 /    0 - Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US) AppleWebKit/534.13 (KHTML, like Gecko) Chrome/9.0.597.107 Safari/534.13 BuiltWith/1.2
    1 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US) AppleWebKit/534.16 (KHTML, like Gecko) Chrome/10.0.648.151 Safari/534.16
    1 /    1 /    0 - Mozilla/5.0 (Windows NT 5.2; rv:12.0) Gecko/20100101 Firefox/12.0
    1 /    1 /    0 - Opera/9.80 (Windows NT 5.1; U; MRA 8.0 (build 5784); ru) Presto/2.10.289 Version/12.02
    1 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US) AppleWebKit/534.10 (KHTML, like Gecko) Chrome/8.0.552.224 Safari/534.10
    1 /    1 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_2) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/21.0.1180.82 Safari/537.1
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.2; rv:14.0) Gecko/20100101 Firefox/14.0.1
    1 /    0 /    0 - Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0; KB974488)
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; MRA 5.10 (build 5218); MRSPUTNIK 2, 4, 0, 501; InfoPath.2)
    1 /    0 /    0 - QuerySeekerSpider ( http://queryseeker.com/bot.html )
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.2)
    1 /    1 /    0 - PageAnalyzer/1.3b (www.the-escape.co.uk)
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB7.4; MRA 6.0 (build 6005); User-agent: Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1); .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; InfoPath.1; .NET4.0C; .NET4.0E; MRIE8PACK 2.0.1)
    1 /    1 /    0 - CheckSite Verification Agent ( http://www.checksite.us)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.22 (KHTML, like Gecko) Iron/25.0.1400.0 Chrome/25.0.1400.0 Safari/537.22
    1 /    1 /    0 - EventMachine HttpClient
    1 /    0 /    0 - Mozilla/5.0 (Android; Mobile; rv:20.0.1) Gecko/20.0.1 Firefox/20.0.1
    1 /    1 /    0 - Opera/9.80 (Windows NT 6.1; U; YB/3.5.1; ru) Presto/2.6.30 Version/10.63
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 8.0 (build 5988)) Presto/2.12.388 Version/12.15
    1 /    1 /    0 - Opera/9.80 (Windows NT 6.1; U; Edition Next; ru) Presto/2.11.310 Version/12.50
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; U; Edition Yx; ru) Presto/2.10.289 Version/12.02
    1 /    0 /    0 - Mozilla/5.0 (Linux; U; Android 2.2; fr-fr; Desire_A8181 Build/FRF91) App3leWebKit/53.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; MDDR; .NET4.0C; .NET4.0E; .NET CLR 1.1.4322; Tablet PC 2.0)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 Safari/537.22,gzip(gfe) (via docs.google.com/viewer)
    1 /    1 /    0 - Opera/9.80 (X11; Linux i686) Presto/2.12.388 Version/12.10
    1 /    1 /    0 - Apache-HttpClient/4.2.3 (java 1.5)
    1 /    1 /    0 - Mozilla/5.0 (compatible; NetSeer crawler/2.0;  http://www.netseer.com/crawler.html; <EMAIL>)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1; rv:7.0.1) Gecko/20100101 (C67D51D4-DE8A-25CA-C613-17B73EBB7584) Firefox/7.0.1
    1 /    1 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1229.94 Safari/537.4
    1 /    0 /    0 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B141 Safari/8536.25
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.172 Safari/537.22 CoolNovo/********
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:17.0) Gecko/20100101 Firefox/17.0
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.2; rv:20.0) Gecko/20100101 Firefox/20.0 SeaMonkey/2.17.1
    1 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1) )
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/534.24 (KHTML, like Gecko) Chrome/11.0.696.68 Safari/534.24
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1469.0 Safari/537.36
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.65 Safari/537.36
    1 /    0 /    0 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/6.0; BOIE9;RURU)
    1 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:1.8.1.20) Gecko/20081217 Firefox/2.0.0.20
    1 /    1 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; U; MRA 8.0 (build 5880); ru) Presto/2.10.289 Version/12.02
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.0) Presto/2.12.388 Version/12.15
    1 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:1.9.2.25) Gecko/20111212 Firefox/3.6.25
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.2; WOW64) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1229.79 Safari/537.4
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; MSIE 5.5; Windows NT 5.0) Opera 7.02 Bork-edition [en]
    1 /    1 /    0 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_6_6) AppleWebKit/534.24 (KHTML, like Gecko) Contact: <EMAIL>
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; SLCC1; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30618; .NET4.0C)
    1 /    0 /    0 - Opera/9.80 (Android 4.0.3; Linux; Opera Tablet/ADR-1301080958) Presto/2.11.355 Version/12.10
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; MRSPUTNIK 2, 4, 1, 105; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; AskTbPTV/5.15.1.22229; .NET4.0C)
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; GTB7.2; MRSPUTNIK 2, 4, 1, 105; InfoPath.1)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.1634 Safari/535.19 YI
    1 /    1 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/536.5 (KHTML, like Gecko) YaBrowser/1.1.1084.5409 Chrome/19.1.1084.5409 Safari/536.5
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 6.0 (build 6048)) Presto/2.12.388 Version/12.15
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.2; WOW64; Edition Yx) Presto/2.12.388 Version/12.14
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1) Presto/2.12.388 Version/12.10
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1; U; Edition Next; ru) Presto/2.9.220 Version/12.00
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1; U; MRA 6.0 (build 5680); ru) Presto/2.10.289 Version/12.00
    1 /    1 /    0 - Mozilla/5.0 (Windows NT 6.1; rv:19.0) Gecko/20100101 Firefox/19.0,gzip(gfe)
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 1.1.4322; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/535.2 (KHTML, like Gecko) Chrome/15.0.874.121 Safari/535.2
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 6.0 (build 6015)) Presto/2.12.388 Version/12.15
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; MRA 5.8 (build 4157); .NET CLR 2.0.50727; AskTbPTV/5.11.3.15590)
    1 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:*******) Gecko/2008092417 Firefox/3.0.3 (.NET CLR 3.5.30729)
    1 /    0 /    0 - NSPlayer/12.00.7601.17514 WMFSDK/12.00.7601.17514
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.2)
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; U; MRA 5.9 (build 4953); ru) Presto/2.10.229 Version/11.60
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 5.10 (build 5310)) Presto/2.12.388 Version/12.10
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1; U; Edition Yx; ru) Presto/2.10.229 Version/11.62
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; MDDR; .NET4.0C; .NET4.0E; .NET CLR 1.1.4322; Tablet PC 2.0); 360Spider
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.64 Safari/537.31 AlexaToolbar/alxg-3.1
    1 /    1 /    0 - Microsoft Office Mobile/15.0
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.2; rv:20.0) Gecko/20100101 Firefox/20.0
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1; U; ru) Presto/2.5.22 Version/10.51
    1 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:*******) Gecko/20100625 Firefox/3.6.6 ( .NET CLR 3.5.30729; .NET4.0E)
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; MRSPUTNIK 2, 4, 0, 516; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; Tablet PC 2.0)
    1 /    0 /    0 - Dalvik/1.6.0 (Linux; U; Android 4.1.2; GT-I9100 Build/JZO54K)
    1 /    0 /    0 - Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/6.0; EIE10;RURUWOL)
    1 /    1 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:1.9.0.8) Gecko/2009032609 Firefox/3.0.8
    1 /    0 /    0 - (null)
    1 /    0 /    0 - Mozilla/5.0 (X11; Ubuntu; Linux i686; rv:11.0) Gecko/20100101 Firefox/11.0
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; .NET CLR 1.1.4322)
    1 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; Trident/4.0; .NET CLR 1.1.4322; .NET CLR 2.0.50727)
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/6.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; InfoPath.3; MS-RTC LM 8; BRI/2; .NET4.0E)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; rv:10.0.2) Gecko/20100101 Firefox/10.0.2
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.0; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0
    1 /    0 /    0 - Opera/9.80 (Series 60; Opera Mini/7.1.32449/29.3530; U; ru) Presto/2.8.119 Version/11.10
    1 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US) AppleWebKit/534.7 (KHTML, like Gecko) Chrome/7.0.517.41 Safari/534.7
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.2.2.0 Safari/537.31
    1 /    0 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:1.9.2.10) Gecko/20100914 Firefox/3.6.10 (.NET CLR 3.5.30729)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1105.412 YaBrowser/1.5.1105.412 Safari/537.4
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1300.0 Iron/23.0.1300.0 Safari/537.11
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1; U; ru) Presto/2.10.289 Version/12.02
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.2; .NET4.0C; .NET4.0E; MSOffice 12)
    1 /    0 /    0 - Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; GT-P3100 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30
    1 /    0 /    0 - Opera/9.80 (Windows NT 5.1; U; en) Presto/2.10.289 Version/12.01
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1271.91 Safari/537.11
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1229.79 Safari/537.4
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; U; MRA 6.0 (build 6001); ru) Presto/2.10.289 Version/12.01
    1 /    0 /    0 - Opera/9.64 (Windows NT 5.1; U; ru) Presto/2.1.1
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1) AppleWebKit/535.7 (KHTML, like Gecko) Chrome/16.0.912.63 Safari/535.7
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.0; U; ru) Presto/2.9.168 Version/11.50
    1 /    1 /    0 - Opera/9.80 (Windows NT 6.1; Win64; x64) Presto/2.12.388 Version/12.10
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; U; ru) Presto/2.10.229 Version/11.62
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/535.7 (KHTML, like Gecko) Chrome/16.0.912.63 Safari/535.7 YE
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; InfoPath.3; .NET CLR 2.0.50727)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.0) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/21.0.1180.89 Safari/537.1
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; ru; rv:2.0) Gecko/20100101 Firefox/4.0 Opera 11.60
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; MRA 5.8 (build 4157)) Presto/2.12.388 Version/12.14
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 5.1; en-US) AppleWebKit/535.12 (KHTML, like Gecko) Version/5.0.1 Safari/535.12
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; MRSPUTNIK 2, 4, 0, 501; .NET CLR 2.0.50727; InfoPath.2; .NET CLR 3.0.04506.30)
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; Media Center PC 6.0; .NET4.0C; .NET4.0E; .NET CLR 3.5.30729; .NET CLR 3.0.30729)
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 6.0 (build 5976)) Presto/2.12.388 Version/12.14
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.0) Presto/2.12.388 Version/12.14
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; InfoPath.2; .NET4.0C; .NET4.0E; MSOffice 12)
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; Win64; x64; Edition Yx) Presto/2.12.388 Version/12.11
    1 /    0 /    0 - rarely used
    1 /    1 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.4 (KHTML, like Gecko) Chrome/22.0.1229.94 Safari/537.4
    1 /    1 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/6.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C)
    1 /    0 /    0 - Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.0; Trident/5.0)
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0; MRSPUTNIK 2, 4, 1, 105; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET4.0C; .NET4.0E; Tablet PC 2.0)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.43 Safari/537.31 AlexaToolbar/pDvbmwUg-1.3
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET4.0C; .NET4.0E; NP06)
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0; .NET CLR 1.1.4322)
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.1; WOW64; rv:10.0) Gecko/20100101 Firefox/10.0
    1 /    0 /    0 - Mozilla/5.0 (Windows NT 6.2; rv:20.0) Gecko/20100101 Firefox/20.0
    1 /    0 /    0 - Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/6.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET4.0E; Tablet PC 2.0; MSOffice 12)
    1 /    1 /    0 - Mozilla/5.0 (Windows; U; Windows NT 6.1; rv:11.0) Gecko/20100101 Firefox/11.0
    1 /    0 /    0 - Opera/9.80 (Android 4.0.3; Linux; Opera Mobi/ADY-1305080823) Presto/2.11.355 Version/12.10
    1 /    0 /    0 - Opera/9.80 (J2ME/MIDP; Opera Mini/4.3.13057/29.3530; U; ru) Presto/2.8.119 Version/11.10
    1 /    1 /    0 - Mozilla/5.0 (Windows; U; Windows NT 5.1; ru; rv:********) Gecko/2010031422 Firefox/3.0.19 YB/3.5.1 (.NET CLR 3.5.30729)
    1 /    0 /    0 - Opera/9.80 (Windows NT 6.1; WOW64; MRA 6.0 (build 6042)) Presto/2.12.388 Version/12.15

  botreq / bothits (26916 / 12055) - user_agent (40)
5448 /    0 - msnbot-media/1.1 ( http://search.msn.com/msnbot.htm)
4162 / 2557 - Mozilla/5.0 (compatible; Googlebot/2.1;  http://www.google.com/bot.html)
3202 /    0 - Mozilla/5.0 (compatible; YandexImages/3.0;  http://yandex.com/bots)
3038 / 2351 - Mozilla/5.0 (compatible; bingbot/2.0;  http://www.bing.com/bingbot.htm)
2650 / 2044 - Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_1 like Mac OS X; en-us) AppleWebKit/532.9 (KHTML, like Gecko) Version/4.0.5 Mobile/8B117 Safari/6531.22.7 (compatible; Googlebot-Mobile/2.1;  http://www.google.com/bot.html)
1937 / 1676 - Mozilla/5.0 (compatible; YandexBot/3.0;  http://yandex.com/bots)
1900 /    0 - Googlebot-Image/1.0
1543 / 1408 - Mozilla/5.0 (compatible; SearchBot)
 519 /  280 - SolomonoBot/1.04 (http://www.solomono.ru)
 400 /  387 - Mozilla/5.0 (compatible; Linux i686; Yandex.Gazeta Bot/1.0;  http://gazeta.yandex.ru)
 303 /  293 - Mozilla/5.0 (compatible; Linux x86_64; Mail.RU_Bot/2.0;  http://go.mail.ru/help/robots)
 250 /  218 - msnbot/2.0b ( http://search.msn.com/msnbot.htm)
 244 /    0 - Googlebot/2.1 ( http://www.google.com/bot.html)
 185 /  154 - SAMSUNG-SGH-E250/1.0 Profile/MIDP-2.0 Configuration/CLDC-1.1 UP.Browser/*******.c.1.101 (GUI) MMP/2.0 (compatible; Googlebot-Mobile/2.1;  http://www.google.com/bot.html)
 183 /   61 - Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1;  http://www.changedetection.com/bot.html )
 175 /  148 - DoCoMo/2.0 N905i(c100;TB;W24H16) (compatible; Googlebot-Mobile/2.1;  http://www.google.com/bot.html)
 132 /  113 - Mozilla/5.0 (compatible; Mail.RU_Bot/2.0;  http://go.mail.ru/help/robots)
 121 /   58 - Mozilla/5.0 (compatible; Ezooms/1.0; <EMAIL>)
  75 /   56 - Mozilla/5.0 (compatible; MJ12bot/v1.4.3; http://www.majestic12.co.uk/bot.php? )
  72 /   40 - Mozilla/5.0 (compatible; YandexDirect/3.0;  http://yandex.com/bots)
  51 /   41 - Mozilla/5.0 (compatible; TweetedTimes Bot/1.0;  http://tweetedtimes.com)
  47 /   39 - Wotbox/2.01 ( http://www.wotbox.com/bot/)
  40 /    0 - Mozilla/5.0 (compatible; YandexImageResizer/2.0;  http://yandex.com/bots)
  40 /   19 - rogerbot/1.0 (http://www.seomoz.org/dp/rogerbot, rogerbot-crawler <EMAIL>)
  39 /   19 - Twitterbot/1.0
  32 /   27 - Mozilla/5.0 (compatible; TweetmemeBot/3.0;  http://tweetmeme.com/)
  28 /   24 - Mozilla/5.0 (compatible; PaperLiBot/2.1; http://support.paper.li/entries/20023257-what-is-paper-li)
  26 /   20 - Mozilla/5.0 (compatible; ZumBot/1.0; http://help.zum.com/inquiry)
  19 /    0 - Mozilla/5.0 (compatible; special_archiver/3.1.1  http://www.archive.org/details/archive.org_bot)
  18 /    9 - Mozilla/5.0 (compatible; YandexFavicons/1.0;  http://yandex.com/bots)
   9 /    3 - Mozilla/5.0 (compatible; AhrefsBot/4.0;  http://ahrefs.com/robot/)
   7 /    0 - Mozilla/5.0 (compatible; bingbot/2.0;  http://www.bing.com/bingbot.htm) SitemapProbe
   5 /    3 - msnbot-NewsBlogs/2.0b ( http://search.msn.com/msnbot.htm)
   4 /    3 - Mozilla/5.0 (compatible; Exabot/3.0 (BiggerBetter);  http://www.exabot.com/go/robot)
   3 /    0 - Googlebot-Video/1.0
   3 /    1 - Mozilla/5.0 (compatible; archive.org_bot  http://www.archive.org/details/archive.org_bot)
   2 /    1 - SurcentroBot
   2 /    2 - Mozilla/5.0 (compatible; YandexBot/3.0; MirrorDetector;  http://yandex.com/bots)
   1 /    0 - Linguee Bot (http://www.linguee.com/bot; <EMAIL>)
   1 /    0 - QuerySeekerSpider ( http://queryseeker.com/bot.html )

  mobihits (1107) - user_agent (156)
181 - Mozilla/5.0 (iPad; CPU OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B329 Safari/8536.25
176 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B329 Safari/8536.25
 76 - Mozilla/5.0 (iPad; CPU OS 5_1_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9B206 Safari/7534.48.3
 60 - Mozilla/5.0 (Linux; U; Android 2.2; ru-ru; HTC_Gratia_A6380 Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
 30 - Mozilla/5.0 (Linux; Android 4.0.4; HTC Incredible S Build/IMM76D) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Mobile Safari/537.22
 26 - Mozilla/5.0 (iPod; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B329 Safari/8536.25
 25 - Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; HTC; 7 Mozart T8698)
 24 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A523 Safari/8536.25
 24 - Mozilla/5.0 (iPad; CPU OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A523 Safari/8536.25
 21 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B329
 19 - Mozilla/5.0 (Linux; Android 4.1.2; GT-I9300 Build/JZO54K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19
 18 - Opera/9.80 (Android 4.0.4; Linux; Opera Mobi/ADR-1301080958) Presto/2.11.355 Version/12.10
 17 - Opera/9.80 (Android 4.0.3; Linux; Opera Mobi/ADR-1301080958) Presto/2.11.355 Version/12.10
 17 - Mozilla/5.0 (iPad; CPU OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A8426 Safari/8536.25
 17 - Opera/9.80 (Android 2.3.3; Linux; Opera Mobi/ADR-1212030820) Presto/2.11.355 Version/12.10
 16 - Mozilla/5.0 (Linux; Android 4.1.2; GT-I9100 Build/JZO54K) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Mobile Safari/537.22
 15 - Mozilla/5.0 (Linux; Android 4.0.3; HTC One V Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19
 13 - Mozilla/5.0 (iPod; CPU iPhone OS 6_1_2 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B146 Safari/8536.25
 13 - Opera/9.80 (Android 2.3.4; Linux; Opera Mobi/ADR-1301080958) Presto/2.11.355 Version/12.10
 12 - Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 800)
 11 - Mozilla/5.0 (iPad; CPU OS 6_1_2 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B146 Safari/8536.25
 11 - Mozilla/5.0 (Linux; U; Android 2.3.4; ru-ru; HTC Sensation Z710e Build/GRJ22) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
 11 - Mozilla/5.0 (iPad; CPU OS 5_1_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Mobile/9B206
 11 - Mozilla/5.0 (Linux; U; Android 4.1.2; ru-ru; GT-I9300 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
 10 - Mozilla/5.0 (iPad; CPU OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) CriOS/25.0.1364.124 Mobile/10B329 Safari/8536.25
  9 - Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; SonyEricssonLT26w Build/6.1.A.2.55) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
  9 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_2 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B146
  9 - Mozilla/5.0 (iPad; CPU OS 6_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B141 Safari/8536.25
  8 - Mozilla/5.0 (Linux; U; Android 3.2; ru-ru; GT-P7300 Build/HTJ85B) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13
  8 - Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; NOKIA; Lumia 920)
  7 - Mozilla/5.0 (Linux; U; Android 4.0.3; ru-ru; EVO3D_X515m Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
  7 - Mozilla/5.0 (Linux; Android 4.0.4; ZTE Grand Era Build/IMM76L) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.40 Mobile Safari/537.31 OPR/14.0.1074.54070
  6 - Mozilla/5.0 (Linux; Android 4.1.1; HTC One X  Build/JRO03C) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
  5 - Mozilla/5.0 (Linux; U; Android 2.2.1; ru-ru; HTC Wildfire Build/FRG83D) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  5 - Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; HTC_DesireS_S510e Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
  5 - Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
  5 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B145 [FBAN/FBIOS;FBAV/6.0.2;FBBV/183159;FBDV/iPhone4,1;FBMD/iPhone;FBSN/iPhone OS;FBSV/6.1.1;FBSS/2; FBCR/O2;FBID/phone;FBLC/en_US;FBOP/1]
  5 - Mozilla/5.0 (Linux; U; Android 2.3.4; ru-ru; LG-E510 Build/GRJ22) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  5 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) CriOS/26.0.1410.53 Mobile/10B329 Safari/8536.25
  5 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_4 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B350 Safari/8536.25
  5 - Mozilla/5.0 (iPad; CPU OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) CriOS/26.0.1410.50 Mobile/10B329 Safari/8536.25
  5 - Mozilla/5.0 (Linux; Android 4.0.4; U8836D Build/HuaweiU8836D) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
  5 - Mozilla/5.0 (Linux; Android 4.1.2; GT-I9100 Build/JZO54K) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
  4 - Opera/9.80 (Android; Opera Mini/7.0.29733/29.3271; U; en) Presto/2.8.119 Version/11.10
  4 - Mozilla/5.0 (iPad; CPU OS 5_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Mobile/9B176
  4 - Mozilla/5.0 (Linux; Android 4.0.4; ST26i Build/11.0.A.3.18) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19
  4 - Mozilla/5.0 (Linux; Android 4.2.2; Nexus 7 Build/JDQ39) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Safari/537.22
  4 - Opera/9.80 (Android; Opera Mini/7.5.33286/29.3345; U; en) Presto/2.8.119 Version/11.10
  4 - Mozilla/5.0 (iPad; CPU OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B329
  4 - Mozilla/5.0 (Linux; Android 4.1.2; ST26i Build/11.2.A.0.21) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19
  4 - Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; SP-A20i Build/MF_ICS_02.19) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  3 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_2 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B146 Safari/8536.25
  3 - Mozilla/5.0 (iPod; CPU iPhone OS 6_1_2 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B146
  3 - Opera/9.80 (Android; Opera Mini/6.5.27452/29.3345; U; ru) Presto/2.8.119 Version/11.10
  3 - Opera/9.80 (Android; Opera Mini/7.5.32193/29.3417; U; ru) Presto/2.8.119 Version/11.10
  3 - Mozilla/5.0 (iPad; CPU OS 6_1_2 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B146
  3 - Mozilla/5.0 (Linux; U; Android 4.1.1; ru-ru; Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30
  3 - Mozilla/5.0 (Mobile; rv:18.0) Gecko/18.0 Firefox/18.0
  3 - Mozilla/5.0 (iPhone; CPU iPhone OS 5_1_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9B206 Safari/7534.48.3
  3 - Mozilla/5.0 (iPhone; CPU iPhone OS 5_1_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9B208 Safari/7534.48.3
  3 - Mozilla/5.0 (Linux; U; Android 4.1.2; ru-ru; SonyST26i Build/11.2.A.0.21) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
  3 - Mozilla/5.0 (Android; Tablet; rv:21.0) Gecko/21.0 Firefox/21.0
  3 - Mozilla/5.0 (Linux; Android 4.0.4; ARCHOS 80G9 Build/IMM76D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19
  3 - Mozilla/5.0 (iPad; CPU OS 6_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) CriOS/26.0.1410.50 Mobile/10B141 Safari/8536.25
  3 - Opera/9.80 (Android 2.3.5; Linux; Opera Mobi/ADR-1210241511) Presto/2.11.355 Version/12.10
  2 - Mozilla/5.0 (Linux; Android 4.0.3; U9200 Build/HuaweiU9200) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19
  2 - Mozilla/5.0 (iPod; CPU iPhone OS 6_1_3 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B329
  2 - Mozilla/5.0 (Linux; Android 4.2.2; Galaxy Nexus Build/JDQ39) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.54 Mobile Safari/537.31
  2 - Opera/9.80 (Android; Opera Mini/6.5.29194/29.3271; U; ru) Presto/2.8.119 Version/11.10
  2 - Opera/9.80 (Android; Opera Mini/7.5.33361/29.3594; U; ru) Presto/2.8.119 Version/11.10
  2 - Mozilla/5.0 (Linux; U; Android 2.3.5; ru-ru; HTC_DesireHD_A9191 Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  2 - Mozilla/5.0 (Linux; Android 4.2.2; Nexus 7 Build/JDQ39) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19
  2 - Mozilla/5.0 (Linux; Android 4.0.4; ZTE Grand Era Build/IMM76L) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
  2 - Mozilla/5.0 (Linux; Android 4.1.2; GT-I9300 Build/JZO54K) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
  2 - Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 920)
  2 - Mozilla/5.0 (Linux; U; Android 2.3.6; ru-ru; Liquid MT Build/GRK39F) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  2 - Mozilla/5.0 (Android; Tablet; rv:20.0) Gecko/20.0 Firefox/20.0
  2 - Opera/9.80 (Android 4.1.2; Linux; Opera Mobi/ADR-1301071820) Presto/2.11.355 Version/12.10
  2 - Mozilla/5.0 (Linux; Android 4.0.3; HUAWEI MediaPad Build/HuaweiMediaPad) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Safari/537.31
  2 - Mozilla/5.0 (Linux; U; Android 2.3.5; en-ru; HTC_DesireS_S510e Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  2 - Opera/9.80 (Android 2.3.6; Linux; Opera Mobi/ADR-1301071820) Presto/2.11.355 Version/12.10
  2 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B143 Safari/8536.25
  1 - Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; NOKIA; Lumia 710)
  1 - Mozilla/5.0 (Linux; Android 4.1.2; SAMSUNG-SGH-I727 Build/JZO54K) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Mobile Safari/537.22
  1 - Mozilla/5.0 (Linux; Android 4.0.4; ST25i Build/6.1.1.B.1.10) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
  1 - Mozilla/5.0 (iPad; CPU OS 6_1_2 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) CriOS/26.0.1410.50 Mobile/10B146 Safari/8536.25
  1 - Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; GT-I8350)
  1 - Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; GT-P5100 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30
  1 - Mozilla/5.0 (Android; Mobile; rv:20.0) Gecko/20.0 Firefox/20.0
  1 - Mozilla/5.0 (Linux; U; Android 4.0.3; ru-ru; HTC Sensation Z710e Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
  1 - Mozilla/5.0 (Linux; U; Android 4.0.4; en-ru; IncredibleS_S710e Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
  1 - Mozilla/5.0 (Linux; U; Android 4.1.2; ru-ru; GT-I9070 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
  1 - Opera/9.80 (Android; Opera Mini/7.5.32193/29.3271; U; ru) Presto/2.8.119 Version/11.10
  1 - Mozilla/5.0 (Linux; U; Android 2.3.6; ru-ru; GT-S5830i Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  1 - Mozilla/5.0 (Linux; U; Android 2.3.5; ru-ru; HTC_DesireS_S510e Build/GRJ90) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  1 - Mozilla/5.0 (iPad; CPU OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10A523
  1 - Mozilla/5.0 (Linux; Android 4.2.2; Nexus 7 Build/JDQ39) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Safari/537.31
  1 - Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; IncredibleS_S710e Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
  1 - Opera/9.80 (Android 2.3.4; Linux; Opera Mobi/ADR-1301071820) Presto/2.11.355 Version/12.10
  1 - Opera/9.80 (Android; Opera Mini/7.5.33361/29.3530; U; en) Presto/2.8.119 Version/11.10
  1 - Mozilla/5.0 (Linux; U; Android 4.1.1; ru-ru; NEWMAN N1 Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
  1 - Mozilla/5.0 (Linux; U; Android 2.3.3; ru-ru; LG-P500 Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 MMS/LG-Android-MMS-V1.0/1.2
  1 - Mozilla/5.0 (Linux; U; Android 2.3.7; ru-ru; E15i Build/3.0.1.A.0.145; MiniCM7-2.2.1) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  1 - Mozilla/5.0 (Linux; Android 4.1.1; GT-N8000 Build/JRO03C) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Safari/537.31
  1 - Microsoft Office Mobile/15.0
  1 - Opera/9.80 (Android; Opera Mini/7.5.32193/29.3183; U; ru) Presto/2.8.119 Version/11.10
  1 - Opera/9.80 (Android 2.3.3; Linux; Opera Mobi/ADR-1210091050) Presto/2.11.355 Version/12.10
  1 - Opera/9.80 (Android; Opera Mini/7.5.33361/29.3345; U; en) Presto/2.8.119 Version/11.10
  1 - Microsoft Office Mobile /14.0
  1 - Mozilla/5.0 (Linux; Android 4.1.1; HTC Desire X Build/JRO03C) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
  1 - Mozilla/5.0 (Linux; Android 4.1.2; GT-I8190 Build/JZO54K) AppleWebKit/537.31 (KHTML, like Gecko) Chrome/26.0.1410.58 Mobile Safari/537.31
  1 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Mobile/10B145
  1 - Mozilla/5.0 (Linux; Android 4.1.1; HTC One X Build/JRO03C) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19
  1 - DoCoMo/2.0 P900i(c100;TB;W24H11) (compatible; ichiro/mobile goo; http://search.goo.ne.jp/option/use/sub4/sub4-1/)
  0 - Mozilla/5.0 (iPad; CPU OS 5_0_1 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9A405 Safari/7534.48.3
  0 - Opera/9.80 (Android; Opera Mini/7.5.32200/29.3183; U; en) Presto/2.8.119 Version/11.10
  0 - Mozilla/5.0 (Linux; U; Android 4.0.3; ru-ru; A500 Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30
  0 - Mozilla/5.0 (iPad; U; CPU OS 4_3_3 like Mac OS X; ru-ru) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8J2 Safari/6533.18.5
  0 - Mozilla/5.0 (iPad; U; CPU OS 4_3_5 like Mac OS X; ru-ru) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8L1 Safari/6533.18.5
  0 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_0_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A525 Safari/8536.25
  0 - Opera/9.80 (Android; Opera Mini/7.5.32200/29.3271; U; en) Presto/2.8.119 Version/11.10
  0 - Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_2_1 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Mobile/8C148
  0 - Opera/9.80 (Android; Opera Mini/7.5.32193/29.3134; U; ru) Presto/2.8.119 Version/11.10
  0 - Opera/9.80 (Android 4.0.3; Linux; Opera Tablet/ADR-1301080958) Presto/2.11.355 Version/12.10
  0 - Opera/9.80 (Android; Opera Mini/6.5.29194/29.3507; U; ru) Presto/2.8.119 Version/11.10
  0 - Mozilla/5.0 (Linux; Android 4.1.1; A701 Build/JRO03H) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19
  0 - Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; GT-P3100 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Safari/534.30
  0 - Opera/9.80 (Android; Opera Mini/7.5.33286/29.3345; U; ru) Presto/2.8.119 Version/11.10
  0 - Opera/9.80 (Android; Opera Mini/7.5.32200/29.3345; U; en) Presto/2.8.119 Version/11.10
  0 - Mozilla/5.0 (Linux; Android 4.0.4; MT27i Build/6.1.1.B.1.54) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.169 Mobile Safari/537.22
  0 - Opera/9.80 (Android; Opera Mini/7.5.32195/29.3134; U; ru) Presto/2.8.119 Version/11.10
  0 - Mozilla/5.0 (Linux; U; Android 2.3.6; en-us; GT-I8150 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  0 - Opera/9.80 (Android; Opera Mini/7.0.29530/29.3709; U; ru) Presto/2.8.119 Version/11.10
  0 - Mozilla/5.0 (Linux; U; Android 4.1.2; ru-ru; GT-I9100 Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
  0 - Mozilla/5.0 (Java; U; ru; nokiac5-00) AppleWebKit/530.13 (KHTML, like Gecko) UCBrowser/8.6.0.199/69/444/UCWEB Mobile UNTRUSTED/1.0 3gpp-gba
  0 - Mozilla/5.0 (Linux; U; Android 2.3.6; ru-ru; GT-I9001 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  0 - Opera/9.80 (Android; Opera Mini/7.5.32195/29.3417; U; ru) Presto/2.8.119 Version/11.10
  0 - Dalvik/1.6.0 (Linux; U; Android 4.1.2; GT-I9100 Build/JZO54K)
  0 - Mozilla/5.0 (Linux; Android 4.1.2; GT-N8000 Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.90 Safari/537.36
  0 - Mozilla/5.0 (Linux; U; Android 2.2.1; ru-ru; HTC_Wildfire_A3333 Build/FRG83D) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  0 - MobileSafari/8536.25 CFNetwork/609 Darwin/13.0.0
  0 - Mozilla/5.0 (Android; Tablet; rv:17.0) Gecko/17.0 Firefox/17.0
  0 - Opera/9.80 (Android; Opera Mini/7.5.32200/29.3134; U; en) Presto/2.8.119 Version/11.10
  0 - Mozilla/5.0 (Android; Mobile; rv:20.0.1) Gecko/20.0.1 Firefox/20.0.1
  0 - Opera/9.80 (Android 4.0.3; Linux; Opera Mobi/ADY-1305080823) Presto/2.11.355 Version/12.10
  0 - Opera/9.80 (Android; Opera Mini/7.5.32195/29.3638; U; ru) Presto/2.8.119 Version/11.10
  0 - Mozilla/5.0 (iPhone; CPU iPhone OS 6_1 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10B141 Safari/8536.25
  0 - Opera/9.80 (Android; Opera Mini/7.0.29952/29.3271; U; ru) Presto/2.8.119 Version/11.10
  0 - Mozilla/5.0 (iPad; CPU OS 6_0_1 like Mac OS X; ru-ru) AppleWebKit/534.46.0 (KHTML, like Gecko) CriOS/21.0.1180.82 Mobile/10A523 Safari/7534.48.3
  0 - Opera/9.80 (Android; Opera Mini/7.5.32193/29.3345; U; ru) Presto/2.8.119 Version/11.10
  0 - Mozilla/5.0 (Linux; Android 4.2.1; Galaxy Nexus Build/JOP40D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Mobile Safari/535.19
  0 - Mozilla/5.0 (Linux; U; Android 2.2; fr-fr; Desire_A8181 Build/FRF91) App3leWebKit/53.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1
  0 - MobileSafari/8536.25 CFNetwork/609.1.4 Darwin/13.0.0
  0 - Mozilla/5.0 (Linux; U; Android 4.1.1; ru-ru; HTC_One_S Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
  0 - AndroidDownloadManager
  0 - Mozilla/5.0 (Linux; U; Android 4.0.4; ru-ru; PAP4040_DUO Build/PrestigioPAP4040DUO) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30
