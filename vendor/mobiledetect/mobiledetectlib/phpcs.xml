<?xml version="1.0"?>
<ruleset name="PSR1">
    <description>The PSR-2 coding standard extended.</description>
    <rule ref="PSR1">
        <exclude name="PSR1.Classes.ClassDeclaration.MissingNamespace"/>
    </rule>
    <rule ref="Squiz">
        <exclude name="Squiz.Classes.ValidClassName.NotCamelCaps"/>
        <exclude name="Squiz.Files.FileExtension.ClassFound"/>
        <exclude name="Squiz.Commenting.ClassComment.TagNotAllowed"/>
    </rule>
    <rule ref="Squiz.Strings.DoubleQuoteUsage">
        <type>error</type>
    </rule>
    <rule ref="Generic.Files.LineLength">
        <properties>
            <property name="lineLimit" value="140"/>
        </properties>
    </rule>
</ruleset>