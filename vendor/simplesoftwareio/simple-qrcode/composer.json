{"name": "simplesoftwareio/simple-qrcode", "description": "Simple QrCode is a QR code generator made for Laravel.", "keywords": ["qrcode", "laravel", "simple", "generator", "wrapper"], "homepage": "https://www.simplesoftware.io/#/docs/simple-qrcode", "license": "MIT", "authors": [{"name": "Simple Software LLC", "email": "<EMAIL>"}], "require": {"php": ">=7.2|^8.0", "ext-gd": "*", "bacon/bacon-qr-code": "^2.0"}, "require-dev": {"mockery/mockery": "~1", "phpunit/phpunit": "~9"}, "suggest": {"ext-imagick": "Allows the generation of PNG QrCodes.", "illuminate/support": "Allows for use within Laravel."}, "autoload": {"psr-4": {"SimpleSoftwareIO\\QrCode\\": "src"}}, "scripts": {"test": "phpunit"}, "extra": {"laravel": {"providers": ["SimpleSoftwareIO\\QrCode\\QrCodeServiceProvider"], "aliases": {"QrCode": "SimpleSoftwareIO\\QrCode\\Facades\\QrCode"}}}}