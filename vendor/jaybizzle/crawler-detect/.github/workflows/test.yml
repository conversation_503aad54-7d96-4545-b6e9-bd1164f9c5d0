name: Test

on:
  push:
    branches:
      - "master"
  pull_request:

jobs:
  build:
    runs-on: ubuntu-24.04
    strategy:
      fail-fast: true
      matrix:
        php: [7.1, 7.2, 7.3, 7.4, 8.0, 8.1, 8.2, 8.3, 8.4]

    name: PHP:${{ matrix.php }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup PHP, with composer
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          tools: composer:v2
          coverage: xdebug

      - name: Get composer cache directory
        id: composer-cache
        run: |
          echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT
        shell: bash

      - name: Cache composer dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: dependencies-php-${{ matrix.php }}-composer-${{ hashFiles('composer.json') }}
          restore-keys: dependencies-php-${{ matrix.php }}-composer-

      - name: Install Composer dependencies
        run: |
          composer install --prefer-dist --no-interaction --no-suggest

      - name: Run Unit tests
        run: |
          vendor/bin/phpunit --coverage-clover=tests/logs/clover.xml

      - name: Upload coverage results to Coveralls
        env:
          COVERALLS_REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          composer global require php-coveralls/php-coveralls "^1.0"
          coveralls --coverage_clover=tests/logs/clover.xml -v
