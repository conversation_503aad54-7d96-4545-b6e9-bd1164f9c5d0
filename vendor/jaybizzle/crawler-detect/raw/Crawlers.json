[" YLT", "^Aether", "^Amazon Simple Notification Service Agent$", "^Amazon-Route53-Health-Check-Service", "^Amazon CloudFront", "^b0t$", "^bluefish ", "^Calypso v\\/", "^COMODO DCV", "^Corax", "^DangDang", "^DavClnt", "^DHSH", "^docker\\/[0-9]", "^Expanse", "^FDM ", "^git\\/", "^Goose\\/", "^Grab<PERSON>", "^Gradle\\/", "^HTTPClient\\/", "^HTTPing", "^Java\\/", "^Jeode\\/", "^Jetty\\/", "^Mail\\/", "^Mget", "^Microsoft URL Control", "^Mikrotik\\/", "^Netlab360", "^NG\\/[0-9\\.]", "^NING\\/", "^npm\\/", "^Nuclei", "^PHP-AYMAPI\\/", "^PHP\\/", "^pip\\/", "^pnpm\\/", "^RMA\\/", "^Ruby|Ruby\\/[0-9]", "^symbolicator\\/", "^Swurl ", "^TLS tester ", "^twine\\/", "^ureq", "^VSE\\/[0-9]", "^WordPress\\.com", "^XRL\\/[0-9]", "^ZmEu", "008\\/", "13TABS", "192\\.comAgent", "2GDPR\\/", "2ip\\.ru", "<PERSON><PERSON><PERSON>", "7Siters", "80legs", "a3logics\\.in", "A6-Indexer", "<PERSON><PERSON><PERSON>", "Aboundex", "about<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-AI-Agent", "acebookexternalhit\\/", "acoon", "acrylicapps\\.com\\/pulp", "Acunetix", "AdAuth\\/", "adbeat", "AddThis", "ADmantX", "AdminLabs", "adressendeutschland", "adreview\\/", "adscanner", "adstxt-worker", "Adstxtaggregator", "adstxt\\.com", "Adyen HttpClient", "AffiliateLabz\\/", "affilimate-puppeteer", "agentslug", "AHC", "a<PERSON>it", "aiohttp\\/", "Airmail", "akka-http\\/", "akula\\/", "alertra", "alexa site audit", "Alibaba\\.Security\\.<PERSON><PERSON><PERSON><PERSON>", "Alligator", "allloadin", "AllSubmitter", "alyze\\.info", "amagit", "<PERSON><PERSON><PERSON>", "AndroidDownloadManager", "Anemone", "AngleSharp", "annotate_google", "<PERSON><PERSON><PERSON>", "An<PERSON>is Agent", "Ant\\.com", "AnyEvent-HTTP\\/", "Apache Ant\\/", "Apache Droid", "Apache OpenOffice", "Apache-HttpAsyncClient", "Apache-HttpClient", "ApacheBench", "Apexoo", "apimon\\.de", "APIs-Google", "AportWorm\\/", "AppBeat\\/", "AppEngine-Google", "AppleSyndication", "Aprc\\/[0-9]", "<PERSON><PERSON><PERSON>", "arachnode", "Arachnophilia", "aria2", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Asana\\/", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ASPSeek", "<PERSON><PERSON><PERSON>", "Astute", "asynchttp", "Attach", "attohttpc", "autocite", "AutomaticWPTester", "Autonomy", "awin\\.com", "AWS Security Scanner", "axios\\/", "a\\.pr-cy\\.ru", "B-l-i-t-z-B-O-T", "Backlink-<PERSON>ck", "BacklinkHttpStatus", "BackStreet", "BackupLand", "BackWeb", "Bad-Neighborhood", "<PERSON><PERSON>", "baidu\\.com", "Bandit", "Barracuda Sentinel \\(EE\\)", "basicstate", "BatchFTP", "Battleztar <PERSON>a", "baypup\\/", "BazQux", "BBBike", "BCKLINKS", "BDFetch", "BegunAdvertising", "Bewica-security-scan", "Bidtellect", "BigBozz", "Bigfoot", "biglotron", "BingLocalSearch", "BingPreview", "binlar", "biNu image cacher", "Bitacle", "Bitrix link preview", "biz_Directory", "BKCTwitterUnshortener\\/", "Black Hole", "Blackboard Safeassign", "BlackWidow", "BlockNote\\.Net", "BlogBridge", "Bloglines", "<PERSON><PERSON><PERSON><PERSON>", "BlogPulseLive", "BlogSearch", "Blogtrottr", "BlowFish", "boitho\\.com-dc", "Boost\\.Beast", "BPImageWalker", "Braintree-Webhooks", "Branch Metrics API", "Branch-Passthrough", "Brandprotect", "Brandwatch", "<PERSON><PERSON><PERSON>\\/", "Browsershots", "BUbiNG", "<PERSON>\\/", "<PERSON>", "BuiltWith", "Bullseye", "BunnySlippers", "Burf Search", "Butterfly\\/", "BuzzSumo", "CAAM\\/[0-9]", "caam dot crwlr at gmail dot com", "CakePHP", "Calculon", "Canary%20Mail", "CaretNail", "catexplorador", "CC <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "censys", "centuryb.o.t9[at]gmail.com", "Cerberian <PERSON>s", "CERT\\.at-Statistics-Survey", "cf-facebook", "cg-eye", "changedetection", "ChangesMeter", "Charlotte", "chatterino-api-cache", "CheckHost", "checkprivacy", "CherryPicker", "ChinaClaw", "Chirp\\/", "chkme\\.com", "Chlooe", "Chromaxa", "CirrusExplorer", "CISPA Vulnerability Notification", "CISPA Web Analyser", "Citoid", "CJNetworkQuality", "Clarsent<PERSON>", "clips\\.ua\\.ac\\.be", "Cloud mapping", "CloudEndure", "CloudFlare-AlwaysOnline", "Cloudflare-Healthchecks", "Cloudinary", "cmcm\\.com", "coccoc", "cognitiveseo", "ColdFusion", "colly -", "CommaFeed", "Commons-HttpClient", "commonscan", "contactbigdatafr", "contentkingapp", "Contextual Code Sites Explorer", "convera", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "copyright sheriff", "CopyRightCheck", "Copyscape", "cortex\\/", "Cosmos4j\\.feedback", "Covario-IDS", "Craw\\/", "Crescent", "Criteo", "Crowsnest", "CSHttp", "CSSCheck", "Cula\\/", "curb", "Curious <PERSON>", "curl", "cuwhois\\/", "cybo\\.com", "DAP\\/NetHTTP", "DareBoost", "DatabaseDriverMysqli", "DataCha0s", "DatadogSynthetics", "Datafeedwatch", "Datanyze", "DataparkSearch", "dataprovider", "DataXu", "Daum(oa)?[ \\/][0-9]", "dBpoweramp", "ddline", "deeris", "delve\\.ai", "Demon", "<PERSON><PERSON><PERSON><PERSON>", "developers\\.google\\.com\\/\\+\\/web\\/snippet\\/", "Devil", "<PERSON><PERSON>", "Digincore", "DigitalPebble", "<PERSON><PERSON><PERSON>", "Discourse Forum Onebox", "Dispatch\\/", "Disqus\\/", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dlvr", "<PERSON><PERSON><PERSON><PERSON>", "DNSPod-reporting", "docoloc", "Dolphin http client", "DomainAppender", "DomainLabz", "Domains Project\\/", "Donuts Content Explorer", "dotMailer content retrieval", "dotSemantic", "downforeveryoneorjustme", "Download Wonder", "downnotifier", "DowntimeDetector", "<PERSON><PERSON>", "drupact", "Drupal \\(\\+http:\\/\\/drupal\\.org\\/\\)", "DTS Agent", "dubaiindex", "DuplexWeb-Google", "DynatraceSynthetic", "EARTHCOM", "Easy-Thumb", "EasyDL", "Ebingbong", "ec2linkfinder", "eCairn-Grabber", "eCatch", "ECCP", "eContext\\/", "Ecxi", "EirGrabber", "ElectricMonk", "elefent", "EMail Exractor", "<PERSON><PERSON><PERSON>", "EmailWolf", "Embarcadero", "Embed PHP Library", "<PERSON><PERSON><PERSON>", "endo\\/", "europarchive\\.org", "evc-batch", "EventMachine HttpClient", "Everwall Link Expander", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ExactSearch", "ExaleadCloudview", "Excel\\/", "exif", "ExoRank", "Exploratodo", "Express WebPictures", "Extreme Picture Finder", "EyeNetIE", "ezooms", "facebookcatalog", "facebookexternalhit", "facebookexternalua", "facebookplatform", "fairshare", "Faraday v", "fasthttp", "Faveeo", "Favicon downloader", "faviconarchive", "faviconkit", "FavOrg", "<PERSON><PERSON>", "Feedable\\/", "<PERSON><PERSON><PERSON>", "FeedBooster", "FeedBucket", "FeedBunch\\/", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "feeder", "Feedly", "FeedshowOnline", "Feedshow\\/", "Feedspot", "FeedViewer\\/", "Feedwind\\/", "FeedZcollector", "feeltiptop", "Fetch API", "Fetch\\/[0-9]", "Fever\\/[0-9]", "FHscan", "Fiery%20Feeds", "Filestack", "Fimap", "findlink", "findthat<PERSON>le", "FlashGet", "FlipboardBrowserProxy", "FlipboardProxy", "FlipboardRSS", "Flock\\/", "Florienzh\\/", "fluffy", "Flunky", "<PERSON>nxap<PERSON>", "forensiq", "ForusP", "FoundSeoTool", "fragFINN\\.de", "free thumbnails", "Freeuploader", "FreshRSS", "frontman", "Funnelback", "Fuzz Faster U Fool", "G-i-g-a-b-o-t", "g00g1e\\.net", "ganarvisitas", "gdnplus\\.com", "GeedoProductSearch", "geek-tools", "Genieo", "GentleSource", "GetCode", "Getintent", "GetLinkInfo", "getprismatic", "GetRight", "getroot", "GetURLInfo\\/", "GetWeb", "Geziyor", "Ghost Inspector", "GigablastOpenSource", "GIS-LABS", "github-camo", "GitHub-<PERSON><PERSON>", "github\\.com", "Go http package", "Go [\\d\\.]* package http", "Go!Zilla", "Go-Ahead-Got-It", "Go-http-client", "go-mtasts\\/", "gobuster", "gobyus", "Gofeed", "gofetch", "Goldfire Server", "GomezAgent", "gooblog", "<PERSON><PERSON>\\/", "Google AppsViewer", "Google Desktop", "Google favicon", "Google Keyword Suggestion", "Google Keyword Tool", "Google Page Speed Insights", "Google PP Default", "Google Search Console", "Google Web Preview", "Google-Ads", "Google-Adwords", "Google-Apps-Script", "Google-Calendar-Importer", "Google-HotelAdsVerifier", "Google-HTTP-Java-Client", "Google-InspectionTool", "Google-Podcast", "Google-Publisher-Plugin", "Google-Read-<PERSON><PERSON>", "Google-SearchByImage", "Google-Site-Verification", "Google-SMTP-STS", "Google-speakr", "Google-Structured-Data-Testing-Tool", "Google-Transparency-Report", "google-xrawler", "Google-Youtube-Links", "GoogleDocs", "GoogleHC\\/", "GoogleOther", "GoogleProber", "GoogleProducer", "GoogleSites", "Gookey", "GoSpotCheck", "gosquared-thumbnailer", "<PERSON><PERSON>", "GoZilla", "grabify", "GrabNet", "<PERSON><PERSON>", "Grammarly", "GrapeFX", "GreatNews", "<PERSON><PERSON><PERSON>", "GRequests", "grokkit", "grouphigh", "grub-client", "gSOAP\\/", "GT::WWW", "GTmetrix", "GuzzleHttp", "gvfs\\/", "HAA(A)?RTLAND http client", "Haansoft", "hackney\\/", "Hadi Agent", "HappyApps-WebCheck", "Hardenize", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "HaxerMen", "HEADMasterSEO", "HeartRails_Capture", "help@dataminr\\.com", "<PERSON><PERSON><PERSON>", "Hexometer", "historious", "hkedcity", "hledejLevne\\.cz", "Hloader", "HMView", "<PERSON>", "HonesoSearchEngine", "HootSuite Image proxy", "Hootsuite-WebFeed", "hosterstats", "HostTracker", "ht:\\/\\/check", "htdig", "HTMLparser", "htmlyse", "HTTP Banner Detection", "http-get", "HTTP-Header-Abfrage", "http-kit", "http-request\\/", "HTTP-Tiny", "HTTP::Lite", "http:\\/\\/www.neomo.de\\/", "HttpComponents", "httphr", "HTTPie", "HTTPMon", "httpRequest", "httpscheck", "httpssites_power", "httpunit", "HttpUrlConnection", "http\\.rb\\/", "HTTP_Compression_Test", "http_get", "http_request2", "http_requester", "httrack", "huaweisymantec", "HubSpot ", "HubSpot-Link-Resolver", "Humanlinks", "i2kconnect\\/", "Iblog", "<PERSON><PERSON><PERSON>", "Id-search", "IdeelaborPlagiaat", "IDG Twitter Links Resolver", "IDwhois\\/", "<PERSON><PERSON><PERSON>", "igdeSpyder", "iGooglePortal", "IlTrovatore", "Image Fetch", "Image Sucker", "ImageEngine\\/", "ImageVisu\\/", "<PERSON><PERSON><PERSON><PERSON>", "imagineeasy", "imgsizer", "InAGist", "inbound\\.li parser", "InDesign%20CC", "Indy Library", "InetURL", "infegy", "infohelfer", "InfoTekies", "InfoWizards Reciprocal Link", "inpwrd\\.com", "instabid", "Instapaper", "Integrity", "integromedb", "Intelliseek", "InterGET", "Internet Ninja", "InternetSeer", "internetVista monitor", "internetwache", "internet_archive", "intraVnews", "IODC", "IOI", "Inboxb0t", "iplabel", "ips-agent", "IPS\\/[0-9]", "IPWorks HTTP\\/S Component", "iqdb\\/", "<PERSON><PERSON>", "Irokez", "isitup\\.org", "<PERSON><PERSON><PERSON>", "isUp\\.li", "iThemes Sync\\/", "IZaBEE", "iZSearch", "JAHHO", "jan<PERSON>an", "J<PERSON>\\/", "Java.*outbrain", "javelin\\.io", "Jbrofuzz", "Jersey\\/", "JetCar", "Jigsaw", "Job<PERSON><PERSON>", "JobFeed discovery", "Jobg8 URL Monitor", "jobo", "Jobrapido", "Jobsearch1\\.5", "JoinVision Generic", "JolokiaPwn", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "JS-Kit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Just<PERSON>iew", "Kaspersky Lab CFR link resolver", "Kelny\\/", "Kerr<PERSON>\\/", "KeyCDN", "Keyword Density", "Keywords Research", "khttp\\/", "KickFire", "KimonoLabs\\/", "Kml-Google", "knows\\.is", "KOCMOHABT", "kou<PERSON>", "krawler\\.dk", "kube-probe", "kubectl", "kulturarw3", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lavf\\/", "leakix\\.net", "LeechFTP", "LeechGet", "letsencrypt", "Lftp", "LibVLC", "LibWeb", "Libwhisker", "libwww", "Licorne", "Liferea\\/", "Lighthouse", "Lightspeedsystems", "Likse", "limber\\.io", "Link <PERSON>", "LinkAlarm\\/", "Link<PERSON><PERSON><PERSON><PERSON>", "link-check", "linkCheck", "linkdex", "LinkExaminer", "linkfluence", "linkpeek", "LinkPreview", "LinkScan", "LinksManager", "Link<PERSON>iger", "<PERSON><PERSON><PERSON><PERSON>", "link_thumbnailer", "<PERSON><PERSON><PERSON>", "Litem<PERSON>_walker", "livedoor ScreenShot", "LoadImpactRload", "localsearch-web", "LongURL API", "longurl-r-package", "looid\\.com", "looksystems\\.net", "lscache_runner", "ltx71", "lua-resty-http", "Lucee \\(CFML Engine\\)", "Lush Http Client", "lwp-request", "lwp-trivial", "LWP::Simple", "lycos", "LYT\\.SR", "L\\.webis", "mabontland", "MacOutlook\\/", "MagentaNews\\/", "Mag-Net", "MagpieRSS", "Mail::STS", "MailChimp", "Mail\\.Ru", "Majestic12", "makecontact\\/", "Mandrill", "MapperCmd", "marketinggrader", "MarkMonitor", "Mark<PERSON><PERSON>", "Mass Downloader", "masscan\\/", "<PERSON>", "mattermost", "MatchorySearch\\/", "Mediametric", "Mediapartners-Google", "mediawords", "MegaIndex\\.ru", "MeltwaterNews", "<PERSON><PERSON>", "MemGator", "<PERSON><PERSON><PERSON><PERSON>", "MetaURI", "<PERSON><PERSON>_Tear_<PERSON>ple", "Microsearch", "Microsoft Data Access", "Microsoft Office", "Microsoft Outlook", "Microsoft Windows Network Diagnostics", "Microsoft-WebDAV-MiniRedir", "Microsoft\\.Data\\.Mashup", "MicrosoftPreview", "MIDown tool", "MIIxpc", "Mindjet", "Miniature\\.io", "Miniflux", "mio_httpc", "Miro-HttpClient", "Mister PiX", "mixdata dot com", "mixed-content-scan", "mixnode", "Mnogosearch", "mog<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mojolicious \\(Perl\\)", "<PERSON><PERSON>", "monitis", "Monitority\\/", "Monit\\/", "montastic", "MonSpark", "MonTools", "Moreover", "Morfeus Fucking Scanner", "Morning Paper", "MovableType", "mowser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Mr\\.4x3 Powered", "MS Web Services Client Protocol", "MSFrontPage", "mShots", "MuckRack\\/", "muhstik-scan", "MVAClient", "MxToolbox\\/", "myseosnapshot", "nagios", "Najdi\\.si", "Name Intelligence", "NameFo\\.com", "Nameprotect", "nationalarchives", "Navroad", "nbertaupete95", "NearSite", "<PERSON>le", "<PERSON><PERSON><PERSON>", "Net Vampire", "NetAnts", "NETCRAFT", "NetLyzer", "NetMechanic", "NetNewsWire", "Netpursual", "netresearch", "NetShelter ContentScan", "Netsparker", "NetSystemsResearch", "nettle", "NetTrack", "Netvibes", "NetZIP", "Neustar WPM", "NeutrinoAPI", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NewsBlur .*Finder", "NewsGator", "newsme", "newspaper\\/", "Nexgate Ruby Client", "NG-Search", "nghttp2", "Nibbler", "NICErsPRO", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "nineconnections", "NLNZ_IAHarvester", "Nmap Scripting Engine", "node-fetch", "node-superagent", "node-urllib", "Nodemeter", "NodePing", "node\\.io", "nominet\\.org\\.uk", "nominet\\.uk", "Norton-Safeweb", "Notifixious", "notify<PERSON><PERSON>", "NotionEmbedder", "nuhk", "nutch", "<PERSON><PERSON><PERSON>", "nWormFeedFinder", "nyawc\\/", "<PERSON><PERSON><PERSON>", "NYU", "Observatory\\/", "Ocelli\\/", "Octopus", "oegp", "Offline Explorer", "Offline Navigator", "OgScrper", "okhttp", "omgili", "OMSC", "Online Domain Tools", "Open Source RSS", "OpenCalaisSemanticProxy", "Openfind", "OpenLinkProfiler", "Openstat\\/", "OpenVAS", "OPPO A33", "Optimizer", "Orbiter", "OrgProbe\\/", "orion-semantics", "Outlook-Express", "Outlook-iOS", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ownCloud News", "ow\\.ly", "OxfordCloudService", "page scorer", "<PERSON>", "page2rss", "Page<PERSON><PERSON>zer", "PageGrabber", "PagePeeker", "PageScorer", "Pagespeed\\/", "PageThing", "page_verifier", "Panopta", "panscient", "<PERSON>", "parsijoo", "<PERSON><PERSON><PERSON>", "PayPal IPN", "p<PERSON><PERSON><PERSON><PERSON>", "Pcore-HTTP", "PDF24 URL To PDF", "Pearltrees", "PECL::HTTP", "peerindex", "<PERSON><PERSON><PERSON>", "PeoplePal", "Perlu -", "PhantomJS Screenshoter", "PhantomJS\\/", "Photon\\/", "php-requests", "phpservermon", "Pi-Monster", "Picscout", "Picsearch", "PictureFinder", "Pi<PERSON><PERSON>", "Pingability", "PingAdmin\\.Ru", "<PERSON><PERSON>", "Pingoscope", "PingSpot", "ping\\.blo\\.gs", "pinterest\\.com", "<PERSON><PERSON><PERSON>", "Pizilla", "Plagger\\/", "Pleroma ", "Ploetz \\+ Zeller", "Plukkie", "plumanalytics", "PocketImageCache", "Pocket<PERSON><PERSON>er", "<PERSON><PERSON>", "PodcastAddict\\/", "POE-Component-Client-HTTP", "Polymail\\/", "Pompos", "Porkbun", "Port Monitor", "<PERSON><PERSON>", "postfix-mta-sts-resolver", "PostmanRuntime", "postplanner\\.com", "PostPost", "postrank", "PowerPoint\\/", "Prebid", "Prerender", "Priceonomics Analysis Engine", "PrintFriendly", "PritTorrent", "Prlog", "probely\\.com", "probethenet", "Project ?25499", "Project-Resonance", "prospectb2b", "Protopage", "ProWebWalker", "proximic", "PRTG Network Monitor", "pshtt, https scanning", "PTST ", "PTST\\/[0-9]+", "pulsetic\\.com", "Pump", "Python-httplib2", "python-httpx", "python-requests", "Python-urllib", "<PERSON><PERSON>", "QQDownload", "QrafterPro", "<PERSON><PERSON><PERSON>", "Qualidator", "Query<PERSON>", "queuedriver", "quic-go-HTTP\\/", "QuiteRSS", "Quora Link Preview", "Qwantify", "Radian6", "RadioPublicImageResizer", "Railgun\\/", "RankActive", "RankFlex", "RankSonicSiteAuditor", "RapidLoad\\/", "Re-re Studio", "ReactorNetty", "Readability", "RealDownload", "RealPlayer%20Downloader", "RebelMouse", "Recorder", "RecurPost\\/", "redback\\/", "ReederForMac", "<PERSON><PERSON>\\/", "ReGet", "RepoMonkey", "request\\.js", "reqwest\\/", "ResponseCodeTest", "RestSharp", "Riddler", "Rival IQ", "<PERSON><PERSON><PERSON><PERSON>", "Robozilla", "ROI Hunter", "RPT-HTTPClient", "RSSMix\\/", "RSSOwl", "RuxitSynthetic", "RyowlEngine", "safe-agent-scanner", "SalesIntelligent", "Saleslift", "SAP NetWeaver Application Server", "SauceNAO", "SBIder", "sc-downloader", "scalaj-http", "Scamadviser-Frontend", "<PERSON>an<PERSON><PERSON><PERSON>", "scan\\.lol", "<PERSON><PERSON>", "scooter", "ScopeContentAG-HTTP-Client", "ScoutJet", "ScoutURLMonitor", "ScrapeBox Page Scanner", "Scrapy", "Screaming", "ScreenShotService", "<PERSON><PERSON><PERSON>", "Scrutiny\\/", "Search37", "searchenginepromotionhelp", "Searchestate", "SearchExpress", "SearchSight", "SearchWP", "search\\.thunderstone", "Seeker", "semanticdiscovery", "semanticjuice", "Semiocast HTTP client", "Semrush", "Sendsay\\.Ru", "sentry\\/", "<PERSON><PERSON>", "<PERSON><PERSON>", "seo-nastroj\\.cz", "seo4ajax", "Seobility", "SEOCentro", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seocompany", "SEOkicks", "SEOlizer", "Seomoz", "SEOprofiler", "seoscanners", "SEOsearch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SEOstats", "servernfo", "sexsearcher", "Seznam", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shoppimon", "ShopWiki", "ShortLinkTranslate", "shortURL lengthener", "shrinktheweb", "Sideqik", "Siege", "SimplePie", "SimplyFast", "<PERSON><PERSON>", "SISTRIX", "Site Sucker", "Site-Shot\\/", "Site24x7", "SiteBar", "Sitebeam", "Sitebulb\\/", "SiteCondor", "SiteExplorer", "<PERSON><PERSON><PERSON><PERSON>", "Siteimprove", "SiteIndexed", "Sitemap(s)? Generator", "SitemapGenerator", "SiteMonitor", "Siteshooter B0t", "SiteSnagger", "SiteSucker", "SiteTruth", "Sitevigil", "sitexy\\.com", "SkypeUriPreview", "Slack\\/", "sli-systems\\.com", "slider\\.com", "slurp", "SlySearch", "SmartDownload", "SMRF URL Expander", "SMUrlExpander", "Snake", "Snappy", "SnapSearch", "S<PERSON><PERSON>\\/", "SniffRSS", "sniptracker", "Snoopy", "SnowHaze Search", "sogou web", "SortSite", "Sottopop", "sovereign\\.ai", "SpaceBison", "SpamExperts", "<PERSON><PERSON><PERSON>", "<PERSON>nner", "Spawning-AI", "spaziodati", "SPDYCheck", "Specificfeeds", "SpeedKit", "speedy", "SPEng", "Spinn3r", "spray-can", "Sprinklr ", "spyonweb", "sqlmap", "Sqlworm", "Sqworm", "SSL Labs", "ssl-tools", "StackRambler", "Statastico\\/", "Statically-", "StatusCake", "<PERSON><PERSON>", "Stratagems Kumo", "Stripe\\/", "Stroke\\.cz", "StudioFACA", "StumbleUpon", "suchen", "<PERSON><PERSON><PERSON>", "summify", "SuperHTTP", "<PERSON><PERSON><PERSON> Scout", "Su<PERSON><PERSON>", "swcd ", "Symfony BrowserKit", "Symfony2 BrowserKit", "Synapse\\/", "Syndirella\\/", "SynHttpClient-Built", "<PERSON><PERSON><PERSON><PERSON>", "sysscan", "Szukacz", "T0PHackTeam", "tAkeOut", "Tarantula\\/", "Taringa UGC", "Tarmot<PERSON><PERSON><PERSON>", "tchelebi\\.io", "techiaith\\.cymru", "Teleport", "Telesoft", "Telesphoreo", "Telesphorep", "Tenon\\.io", "teoma", "terrainformatica", "Test Certificate Info", "testuri", "Tetrahedron", "TextRazor Downloader", "The Drop Reaper", "The Expert HTML Source Viewer", "The Intraformant", "The Knowledge AI", "theinternetrules", "TheNomad", "Thinklab", "<PERSON><PERSON><PERSON>", "Thumbshots", "ThumbSniper", "timewe\\.net", "TinEye", "Tiny Tiny RSS", "TLSProbe\\/", "<PERSON><PERSON>", "topster", "touche\\.com", "Traackr\\.com", "tracemyfile", "Trackuity", "TrapitAgent", "Trendiction", "Trendsmap", "trendspottr", "truwoGPS", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Turingos", "Turnitin", "tweetedtimes", "Tweetminster", "Twe<PERSON>ler\\/", "twibble", "Twice", "<PERSON>wi<PERSON>", "Twingly", "Twisted PageGetter", "Typhoeus", "ubermetrics-technologies", "uclassify", "UdmSearch", "ultimate_sitemap_parser", "unchaos", "unirest-java", "UniversalFeedParser", "unshortenit", "Unshorten\\.It", "Untiny", "UnwindFetchor", "updated", "updown\\.io daemon", "Upflow", "Uptimia", "URL Verifier", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "URLitor", "urlresolver", "Urlstat", "URLTester", "UrlTrends Ranking Updater", "URLy Warning", "URLy\\.Warning", "URL\\/Emacs", "Vacuum", "Vagabondo", "VB Project", "vBSEO", "VCI", "<PERSON>", "via ggpht\\.com GoogleImageProxy", "Virusdie", "visionutils", "Visual Rights Group", "vkShare", "VoidEYE", "Voil", "voltron", "voyager\\/", "VSAgent\\/", "VSB-TUO\\/", "<PERSON><PERSON>n<PERSON><PERSON>", "VYU2", "w3af\\.org", "W3C-checklink", "W3C-mobileOK", "W3C_Unicorn", "WAC-OFU", "WakeletLinkExpander", "WallpapersHD", "Wallpapers\\/[0-9]+", "wangling", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WatchMouse", "WbSrch\\/", "WDT\\.io", "Web Auto", "Web Collage", "Web Enhancer", "Web Fetch", "Web Fuck", "Web Pix", "Web Sauger", "Web spyder", "Web Sucker", "web-capture\\.net", "Web-sniffer", "Webalta", "Webauskunft", "WebAuto", "WebCapture", "WebClient\\/", "webcollage", "WebCookies", "WebCopier", "WebCorp", "WebDataStats", "WebDoc", "WebEnhancer", "WebFetch", "WebFuck", "WebGazer", "WebGo IS", "WebImageCollector", "WebImages", "WebIndex", "webkit2png", "<PERSON><PERSON><PERSON><PERSON>", "webmastercoffee", "webmon ", "WebPix", "WebReaper", "WebSauger", "webscreenie", "Webshag", "Webshot", "Website Quester", "websitepulse agent", "WebsiteQuester", "Websnapr", "WebSniffer", "<PERSON>", "WebStripper", "WebSucker", "webtech\\/", "WebThumbnail", "Webthumb\\/", "WebWhacker", "WebZIP", "WeLikeLinks", "WEPA", "WeSEE", "wf84", "Wfuzz\\/", "wget", "WhatCMS", "WhatsApp", "WhatsMyIP", "WhatWeb", "WhereGoes\\?", "<PERSON><PERSON><PERSON><PERSON>", "WhoAPI\\/", "WhoRunsCoinHive", "<PERSON><PERSON>", "Windows-RSS-Platform", "WinHttp-Autoproxy-Service", "WinHTTP\\/", "<PERSON><PERSON><PERSON><PERSON>", "wkhtmlto", "wmtips", "<PERSON><PERSON>", "Wolfram HTTPClient", "woorankreview", "WordPress\\/", "WordupinfoSearch", "Word\\/", "worldping-api", "wotbox", "WP Engine Install Performance API", "WP Rocket", "wpif", "wprecon\\.com survey", "WPScan", "wscheck", "Wtrace", "WWW-Collector-E", "WWW-Mechanize", "WWW::Document", "WWW::Mechanize", "WWWOFFLE", "www\\.monitor\\.us", "x09Mozilla", "x22Mozilla", "XaxisSemanticsClassifier", "XenForo\\/", "<PERSON><PERSON><PERSON>", "XING-contenttabreceiver", "xpymep([0-9]?)\\.exe", "Y!J-[A-Z][A-Z][A-Z]", "<PERSON><PERSON><PERSON>", "yacy", "Yahoo Link Preview", "YahooCacheSystem", "YahooMailProxy", "YahooYSMcm", "YandeG", "Yandex(?!Search)", "yanga", "yeti", "Yo-yo", "Yoleo Consumer", "yomins\\.com", "yoogliFetchAgent", "YottaaMonitor", "Your-Website-Sucks", "yourls\\.org", "YoYs\\.net", "YP\\.PL", "Zabbix", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zapier", "Zauba", "Zemanta Aggregator", "Zend\\\\Http\\\\Client", "Zend_Http_Client", "Zermelo", "Zeus ", "zgrab", "ZnajdzFoto", "ZnHTTP", "Zombie\\.js", "Zoom\\.Mac", "ZoteroTranslationServer", "ZyBorg", "[a-z0-9\\-_]*(bot|crawl|headless|archiver|transcoder|spider|uptime|validator|fetcher|cron|checker|reader|extractor|monitoring|analyzer|scraper)"]