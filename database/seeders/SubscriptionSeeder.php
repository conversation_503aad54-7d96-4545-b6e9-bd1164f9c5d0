<?php

namespace Database\Seeders;

use App\Models\Subscription;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SubscriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $subscriptions = [
            [
                'name' => 'Free',
                'description' => 'Basic URL shortening with limited features',
                'price' => 0.00,
                'billing_cycle' => 'monthly',
                'url_limit' => 100,
                'click_limit' => 1000,
                'custom_domains' => false,
                'analytics' => true,
                'api_access' => false,
                'bulk_operations' => false,
                'password_protection' => false,
                'expiration_dates' => false,
                'features' => [
                    'QR Code Generation',
                    'Basic Analytics',
                    'Social Media Sharing'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Pro',
                'description' => 'Advanced features for professionals',
                'price' => 9.99,
                'billing_cycle' => 'monthly',
                'url_limit' => 1000,
                'click_limit' => 10000,
                'custom_domains' => true,
                'analytics' => true,
                'api_access' => true,
                'bulk_operations' => true,
                'password_protection' => true,
                'expiration_dates' => true,
                'features' => [
                    'Custom Domains',
                    'Advanced Analytics',
                    'API Access',
                    'Password Protection',
                    'Bulk Operations',
                    'Link Expiration'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Premium',
                'description' => 'Unlimited features for enterprises',
                'price' => 29.99,
                'billing_cycle' => 'monthly',
                'url_limit' => null, // unlimited
                'click_limit' => null, // unlimited
                'custom_domains' => true,
                'analytics' => true,
                'api_access' => true,
                'bulk_operations' => true,
                'password_protection' => true,
                'expiration_dates' => true,
                'features' => [
                    'Unlimited URLs',
                    'Unlimited Clicks',
                    'Multiple Custom Domains',
                    'Advanced Analytics & Reports',
                    'Priority API Access',
                    'White-label Solution',
                    'Priority Support'
                ],
                'is_active' => true,
            ],
        ];

        foreach ($subscriptions as $subscription) {
            Subscription::create($subscription);
        }
    }
}
