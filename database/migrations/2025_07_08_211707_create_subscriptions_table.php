<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Free, Pro, Premium
            $table->text('description')->nullable();
            $table->decimal('price', 8, 2)->default(0);
            $table->enum('billing_cycle', ['monthly', 'yearly'])->default('monthly');
            $table->integer('url_limit')->nullable(); // null = unlimited
            $table->integer('click_limit')->nullable(); // null = unlimited
            $table->boolean('custom_domains')->default(false);
            $table->boolean('analytics')->default(true);
            $table->boolean('api_access')->default(false);
            $table->boolean('bulk_operations')->default(false);
            $table->boolean('password_protection')->default(false);
            $table->boolean('expiration_dates')->default(false);
            $table->json('features')->nullable(); // Additional features as JSON
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
