<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class CustomDomain extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'domain',
        'is_verified',
        'verification_token',
        'verified_at',
        'ssl_enabled',
        'ssl_expires_at',
        'is_active',
        'dns_records',
    ];

    protected function casts(): array
    {
        return [
            'is_verified' => 'boolean',
            'verified_at' => 'datetime',
            'ssl_enabled' => 'boolean',
            'ssl_expires_at' => 'datetime',
            'is_active' => 'boolean',
            'dns_records' => 'array',
        ];
    }

    /**
     * Get the user that owns the custom domain.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the URLs for the custom domain.
     */
    public function urls(): HasMany
    {
        return $this->hasMany(Url::class);
    }

    /**
     * Generate verification token.
     */
    public static function generateVerificationToken(): string
    {
        return 'ml_verify_' . Str::random(32);
    }

    /**
     * Mark domain as verified.
     */
    public function markAsVerified(): void
    {
        $this->update([
            'is_verified' => true,
            'verified_at' => now(),
        ]);
    }
}
