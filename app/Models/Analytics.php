<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Analytics extends Model
{
    use HasFactory;

    protected $fillable = [
        'url_id',
        'ip_address',
        'user_agent',
        'referer',
        'country',
        'country_name',
        'city',
        'region',
        'device_type',
        'browser',
        'browser_version',
        'platform',
        'platform_version',
        'is_bot',
        'clicked_at',
    ];

    protected function casts(): array
    {
        return [
            'is_bot' => 'boolean',
            'clicked_at' => 'datetime',
        ];
    }

    /**
     * Get the URL that owns the analytics.
     */
    public function url(): BelongsTo
    {
        return $this->belongsTo(Url::class);
    }
}
