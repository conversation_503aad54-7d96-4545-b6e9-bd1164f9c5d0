<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'price',
        'billing_cycle',
        'url_limit',
        'click_limit',
        'custom_domains',
        'analytics',
        'api_access',
        'bulk_operations',
        'password_protection',
        'expiration_dates',
        'features',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'price' => 'decimal:2',
            'custom_domains' => 'boolean',
            'analytics' => 'boolean',
            'api_access' => 'boolean',
            'bulk_operations' => 'boolean',
            'password_protection' => 'boolean',
            'expiration_dates' => 'boolean',
            'features' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the users for the subscription.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Check if subscription is free.
     */
    public function isFree(): bool
    {
        return $this->price == 0;
    }

    /**
     * Check if subscription has feature.
     */
    public function hasFeature(string $feature): bool
    {
        return $this->$feature ?? false;
    }
}
