<?php

namespace App\Http\Controllers;

use App\Models\Url;
use App\Services\QrCodeService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class QrCodeController extends Controller
{
    private QrCodeService $qrCodeService;

    public function __construct(QrCodeService $qrCodeService)
    {
        $this->qrCodeService = $qrCodeService;
    }

    /**
     * Display QR code for a URL.
     */
    public function show(string $code): Response
    {
        $url = $this->findUrlByCode($code);

        return $this->qrCodeService->getQrCodeResponse($url);
    }

    /**
     * Download QR code for a URL.
     */
    public function download(string $code, Request $request): Response
    {
        $url = $this->findUrlByCode($code);

        $options = [
            'format' => $request->get('format', 'png'),
            'size' => $request->get('size', 300),
            'download' => true,
        ];

        return $this->qrCodeService->getQrCodeResponse($url, $options);
    }

    /**
     * Show QR code customization page.
     */
    public function customize(Url $url): View
    {
        // Check if user can customize this URL's QR code
        if ($url->user_id !== Auth::id()) {
            abort(403, 'Unauthorized to customize this QR code.');
        }

        $qrStats = $this->qrCodeService->getQrCodeStats($url);

        return view('qr.customize', compact('url', 'qrStats'));
    }

    /**
     * Generate custom QR code.
     */
    public function generate(Request $request, Url $url): JsonResponse
    {
        // Check if user can generate QR code for this URL
        if ($url->user_id !== Auth::id()) {
            abort(403, 'Unauthorized to generate QR code for this URL.');
        }

        $request->validate([
            'size' => 'integer|min:100|max:1000',
            'margin' => 'integer|min:0|max:10',
            'format' => 'in:png,svg',
            'background_color' => 'array|size:3',
            'background_color.*' => 'integer|min:0|max:255',
            'foreground_color' => 'array|size:3',
            'foreground_color.*' => 'integer|min:0|max:255',
            'error_correction' => 'in:L,M,Q,H',
        ]);

        try {
            $options = [
                'size' => $request->get('size', 300),
                'margin' => $request->get('margin', 2),
                'format' => $request->get('format', 'png'),
                'background_color' => $request->get('background_color', [255, 255, 255]),
                'foreground_color' => $request->get('foreground_color', [0, 0, 0]),
                'error_correction' => $request->get('error_correction', 'M'),
            ];

            $filename = $this->qrCodeService->generateQrCode($url, $options);

            return response()->json([
                'success' => true,
                'message' => 'QR code generated successfully',
                'data' => [
                    'filename' => $filename,
                    'url' => asset('storage/' . $filename),
                    'download_url' => route('qr.download', $url->custom_alias ?: $url->short_code),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate QR code: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate QR code with logo.
     */
    public function generateWithLogo(Request $request, Url $url): JsonResponse
    {
        // Check if user can generate QR code for this URL
        if ($url->user_id !== Auth::id()) {
            abort(403, 'Unauthorized to generate QR code for this URL.');
        }

        $request->validate([
            'logo' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'size' => 'integer|min:100|max:1000',
            'logo_size' => 'integer|min:20|max:200',
        ]);

        try {
            $logoPath = $request->file('logo')->store('temp', 'public');
            $fullLogoPath = storage_path('app/public/' . $logoPath);

            $options = [
                'size' => $request->get('size', 300),
                'logo_size' => $request->get('logo_size', 60),
                'format' => 'png',
            ];

            $filename = $this->qrCodeService->generateQrCodeWithLogo($url, $fullLogoPath, $options);

            // Clean up temporary logo file
            \Storage::disk('public')->delete($logoPath);

            return response()->json([
                'success' => true,
                'message' => 'QR code with logo generated successfully',
                'data' => [
                    'filename' => $filename,
                    'url' => asset('storage/' . $filename),
                    'download_url' => route('qr.download', $url->custom_alias ?: $url->short_code),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate QR code with logo: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate QR codes in multiple formats.
     */
    public function generateMultiple(Request $request, Url $url): JsonResponse
    {
        // Check if user can generate QR code for this URL
        if ($url->user_id !== Auth::id()) {
            abort(403, 'Unauthorized to generate QR codes for this URL.');
        }

        $request->validate([
            'formats' => 'required|array',
            'formats.*' => 'in:png,svg',
        ]);

        try {
            $formats = $request->get('formats', ['png']);
            $results = $this->qrCodeService->generateMultipleFormats($url, $formats);

            return response()->json([
                'success' => true,
                'message' => 'QR codes generated successfully',
                'data' => $results,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate QR codes: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete QR code.
     */
    public function destroy(Url $url): JsonResponse
    {
        // Check if user can delete this URL's QR code
        if ($url->user_id !== Auth::id()) {
            abort(403, 'Unauthorized to delete this QR code.');
        }

        try {
            $deleted = $this->qrCodeService->deleteQrCode($url);

            return response()->json([
                'success' => $deleted,
                'message' => $deleted ? 'QR code deleted successfully' : 'Failed to delete QR code',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete QR code: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Find URL by short code or custom alias.
     */
    private function findUrlByCode(string $code): Url
    {
        $url = Url::where('short_code', $code)
            ->orWhere('custom_alias', $code)
            ->where('is_active', true)
            ->first();

        if (!$url) {
            abort(404, 'Short URL not found.');
        }

        return $url;
    }
}
