<?php

namespace App\Services;

use App\Models\Url;

class SocialSharingService
{
    /**
     * Get social sharing URLs for a given URL.
     */
    public function getSharingUrls(Url $url, array $options = []): array
    {
        $shortUrl = urlencode($url->short_url);
        $title = urlencode($options['title'] ?? $url->title ?? 'Check out this link');
        $description = urlencode($options['description'] ?? $url->description ?? 'Shared via Minilink');
        $hashtags = urlencode($options['hashtags'] ?? 'minilink,urlshortener');

        return [
            'twitter' => $this->getTwitterUrl($shortUrl, $title, $hashtags),
            'facebook' => $this->getFacebookUrl($shortUrl),
            'linkedin' => $this->getLinkedInUrl($shortUrl, $title, $description),
            'tumblr' => $this->getTumblrUrl($shortUrl, $title, $description),
            'reddit' => $this->getRedditUrl($shortUrl, $title),
            'pinterest' => $this->getPinterestUrl($shortUrl, $description),
            'whatsapp' => $this->getWhatsAppUrl($shortUrl, $title),
            'telegram' => $this->getTelegramUrl($shortUrl, $title),
            'email' => $this->getEmailUrl($shortUrl, $title, $description),
            'sms' => $this->getSmsUrl($shortUrl, $title),
        ];
    }

    /**
     * Get Twitter sharing URL.
     */
    private function getTwitterUrl(string $url, string $text, string $hashtags): string
    {
        $params = http_build_query([
            'url' => $url,
            'text' => $text,
            'hashtags' => $hashtags,
        ]);

        return "https://twitter.com/intent/tweet?{$params}";
    }

    /**
     * Get Facebook sharing URL.
     */
    private function getFacebookUrl(string $url): string
    {
        $params = http_build_query([
            'u' => $url,
        ]);

        return "https://www.facebook.com/sharer/sharer.php?{$params}";
    }

    /**
     * Get LinkedIn sharing URL.
     */
    private function getLinkedInUrl(string $url, string $title, string $summary): string
    {
        $params = http_build_query([
            'url' => $url,
            'title' => $title,
            'summary' => $summary,
        ]);

        return "https://www.linkedin.com/sharing/share-offsite/?{$params}";
    }

    /**
     * Get Tumblr sharing URL.
     */
    private function getTumblrUrl(string $url, string $title, string $caption): string
    {
        $params = http_build_query([
            'url' => $url,
            'name' => $title,
            'caption' => $caption,
        ]);

        return "https://www.tumblr.com/widgets/share/tool?{$params}";
    }

    /**
     * Get Reddit sharing URL.
     */
    private function getRedditUrl(string $url, string $title): string
    {
        $params = http_build_query([
            'url' => $url,
            'title' => $title,
        ]);

        return "https://reddit.com/submit?{$params}";
    }

    /**
     * Get Pinterest sharing URL.
     */
    private function getPinterestUrl(string $url, string $description): string
    {
        $params = http_build_query([
            'url' => $url,
            'description' => $description,
        ]);

        return "https://pinterest.com/pin/create/button/?{$params}";
    }

    /**
     * Get WhatsApp sharing URL.
     */
    private function getWhatsAppUrl(string $url, string $text): string
    {
        $message = urlencode("{$text} {$url}");
        
        return "https://wa.me/?text={$message}";
    }

    /**
     * Get Telegram sharing URL.
     */
    private function getTelegramUrl(string $url, string $text): string
    {
        $params = http_build_query([
            'url' => $url,
            'text' => $text,
        ]);

        return "https://t.me/share/url?{$params}";
    }

    /**
     * Get Email sharing URL.
     */
    private function getEmailUrl(string $url, string $subject, string $body): string
    {
        $params = http_build_query([
            'subject' => $subject,
            'body' => "{$body}\n\n{$url}",
        ]);

        return "mailto:?{$params}";
    }

    /**
     * Get SMS sharing URL.
     */
    private function getSmsUrl(string $url, string $text): string
    {
        $message = urlencode("{$text} {$url}");
        
        return "sms:?body={$message}";
    }

    /**
     * Generate social media meta tags for a URL.
     */
    public function generateMetaTags(Url $url, array $options = []): array
    {
        $title = $options['title'] ?? $url->title ?? 'Shortened URL';
        $description = $options['description'] ?? $url->description ?? 'Check out this link shortened with Minilink';
        $image = $options['image'] ?? ($url->qr_code_path ? asset('storage/' . $url->qr_code_path) : null);
        $siteName = $options['site_name'] ?? config('app.name', 'Minilink');

        return [
            // Open Graph (Facebook, LinkedIn)
            'og:title' => $title,
            'og:description' => $description,
            'og:url' => $url->short_url,
            'og:type' => 'website',
            'og:site_name' => $siteName,
            'og:image' => $image,

            // Twitter Card
            'twitter:card' => 'summary_large_image',
            'twitter:title' => $title,
            'twitter:description' => $description,
            'twitter:image' => $image,
            'twitter:url' => $url->short_url,

            // General meta tags
            'title' => $title,
            'description' => $description,
            'canonical' => $url->short_url,
        ];
    }

    /**
     * Get sharing statistics for a URL.
     */
    public function getSharingStats(Url $url): array
    {
        // Get referrer statistics from analytics
        $analytics = $url->analytics()
            ->whereNotNull('referer')
            ->selectRaw('referer, COUNT(*) as count')
            ->groupBy('referer')
            ->orderByDesc('count')
            ->get();

        $socialPlatforms = [
            'facebook.com' => 'Facebook',
            'twitter.com' => 'Twitter',
            't.co' => 'Twitter',
            'linkedin.com' => 'LinkedIn',
            'tumblr.com' => 'Tumblr',
            'reddit.com' => 'Reddit',
            'pinterest.com' => 'Pinterest',
            'whatsapp.com' => 'WhatsApp',
            't.me' => 'Telegram',
        ];

        $socialStats = [];
        $totalSocialClicks = 0;

        foreach ($analytics as $analytic) {
            $domain = parse_url($analytic->referer, PHP_URL_HOST);
            
            foreach ($socialPlatforms as $platformDomain => $platformName) {
                if (strpos($domain, $platformDomain) !== false) {
                    if (!isset($socialStats[$platformName])) {
                        $socialStats[$platformName] = 0;
                    }
                    $socialStats[$platformName] += $analytic->count;
                    $totalSocialClicks += $analytic->count;
                    break;
                }
            }
        }

        return [
            'total_social_clicks' => $totalSocialClicks,
            'platform_breakdown' => $socialStats,
            'social_percentage' => $url->click_count > 0 ? round(($totalSocialClicks / $url->click_count) * 100, 2) : 0,
        ];
    }

    /**
     * Generate sharing buttons HTML.
     */
    public function generateSharingButtons(Url $url, array $platforms = [], array $options = []): string
    {
        if (empty($platforms)) {
            $platforms = ['twitter', 'facebook', 'linkedin', 'whatsapp'];
        }

        $sharingUrls = $this->getSharingUrls($url, $options);
        $buttonClass = $options['button_class'] ?? 'btn btn-outline-primary btn-sm';
        $target = $options['target'] ?? '_blank';
        $showLabels = $options['show_labels'] ?? true;

        $html = '<div class="social-sharing-buttons">';

        $icons = [
            'twitter' => 'fab fa-twitter',
            'facebook' => 'fab fa-facebook',
            'linkedin' => 'fab fa-linkedin',
            'tumblr' => 'fab fa-tumblr',
            'reddit' => 'fab fa-reddit',
            'pinterest' => 'fab fa-pinterest',
            'whatsapp' => 'fab fa-whatsapp',
            'telegram' => 'fab fa-telegram',
            'email' => 'fas fa-envelope',
            'sms' => 'fas fa-sms',
        ];

        $labels = [
            'twitter' => 'Twitter',
            'facebook' => 'Facebook',
            'linkedin' => 'LinkedIn',
            'tumblr' => 'Tumblr',
            'reddit' => 'Reddit',
            'pinterest' => 'Pinterest',
            'whatsapp' => 'WhatsApp',
            'telegram' => 'Telegram',
            'email' => 'Email',
            'sms' => 'SMS',
        ];

        foreach ($platforms as $platform) {
            if (isset($sharingUrls[$platform])) {
                $icon = $icons[$platform] ?? 'fas fa-share';
                $label = $showLabels ? $labels[$platform] : '';
                
                $html .= sprintf(
                    '<a href="%s" target="%s" class="%s" title="Share on %s">
                        <i class="%s"></i> %s
                    </a> ',
                    htmlspecialchars($sharingUrls[$platform]),
                    $target,
                    $buttonClass,
                    $labels[$platform],
                    $icon,
                    $label
                );
            }
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Track social sharing click.
     */
    public function trackSocialClick(Url $url, string $platform, string $ipAddress = null): void
    {
        // You could implement tracking of social sharing clicks here
        // This could be stored in a separate table or added to the analytics table
        
        \Log::info("Social sharing click tracked", [
            'url_id' => $url->id,
            'platform' => $platform,
            'ip_address' => $ipAddress,
            'timestamp' => now(),
        ]);
    }

    /**
     * Get popular sharing platforms.
     */
    public function getPopularPlatforms(): array
    {
        return [
            'twitter' => [
                'name' => 'Twitter',
                'icon' => 'fab fa-twitter',
                'color' => '#1DA1F2',
                'description' => 'Share with your followers on Twitter'
            ],
            'facebook' => [
                'name' => 'Facebook',
                'icon' => 'fab fa-facebook',
                'color' => '#4267B2',
                'description' => 'Share on your Facebook timeline'
            ],
            'linkedin' => [
                'name' => 'LinkedIn',
                'icon' => 'fab fa-linkedin',
                'color' => '#0077B5',
                'description' => 'Share with your professional network'
            ],
            'whatsapp' => [
                'name' => 'WhatsApp',
                'icon' => 'fab fa-whatsapp',
                'color' => '#25D366',
                'description' => 'Share via WhatsApp message'
            ],
            'telegram' => [
                'name' => 'Telegram',
                'icon' => 'fab fa-telegram',
                'color' => '#0088CC',
                'description' => 'Share via Telegram'
            ],
            'email' => [
                'name' => 'Email',
                'icon' => 'fas fa-envelope',
                'color' => '#6c757d',
                'description' => 'Share via email'
            ],
        ];
    }
}
