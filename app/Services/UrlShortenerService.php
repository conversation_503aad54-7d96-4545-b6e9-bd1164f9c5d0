<?php

namespace App\Services;

use App\Models\Url;
use App\Models\User;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class UrlShortenerService
{
    private QrCodeService $qrCodeService;

    public function __construct(QrCodeService $qrCodeService)
    {
        $this->qrCodeService = $qrCodeService;
    }

    /**
     * Shorten a URL.
     */
    public function shortenUrl(array $data, ?User $user = null): Url
    {
        $this->validateUrl($data);

        // Check if custom alias is available
        if (!empty($data['custom_alias'])) {
            $this->validateCustomAlias($data['custom_alias']);
        }

        // Generate short code if no custom alias
        $shortCode = !empty($data['custom_alias']) ? null : Url::generateShortCode();

        // Create URL record
        $url = Url::create([
            'original_url' => $data['original_url'],
            'short_code' => $shortCode,
            'custom_alias' => $data['custom_alias'] ?? null,
            'user_id' => $user?->id,
            'title' => $data['title'] ?? null,
            'description' => $data['description'] ?? null,
            'expires_at' => $data['expires_at'] ?? null,
            'password' => !empty($data['password']) ? bcrypt($data['password']) : null,
            'metadata' => $data['metadata'] ?? null,
        ]);

        // Generate QR code
        $this->generateQrCode($url);

        return $url;
    }

    /**
     * Validate URL data.
     */
    private function validateUrl(array $data): void
    {
        $validator = Validator::make($data, [
            'original_url' => 'required|url|max:2048',
            'custom_alias' => 'nullable|string|max:50|alpha_dash',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:500',
            'expires_at' => 'nullable|date|after:now',
            'password' => 'nullable|string|min:4|max:50',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        // Check for malicious URLs
        if ($this->isMaliciousUrl($data['original_url'])) {
            throw ValidationException::withMessages([
                'original_url' => 'The URL appears to be malicious and cannot be shortened.'
            ]);
        }
    }

    /**
     * Validate custom alias availability.
     */
    private function validateCustomAlias(string $alias): void
    {
        if (Url::where('custom_alias', $alias)->exists() || Url::where('short_code', $alias)->exists()) {
            throw ValidationException::withMessages([
                'custom_alias' => 'This custom alias is already taken.'
            ]);
        }

        // Check against reserved words
        $reserved = ['admin', 'api', 'www', 'mail', 'ftp', 'localhost', 'dashboard', 'analytics'];
        if (in_array(strtolower($alias), $reserved)) {
            throw ValidationException::withMessages([
                'custom_alias' => 'This alias is reserved and cannot be used.'
            ]);
        }
    }

    /**
     * Generate QR code for URL.
     */
    private function generateQrCode(Url $url): void
    {
        try {
            $this->qrCodeService->generateQrCode($url);
        } catch (\Exception $e) {
            // Log error but don't fail the URL creation
            \Log::error('QR Code generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Check if URL is malicious (basic implementation).
     */
    private function isMaliciousUrl(string $url): bool
    {
        $maliciousDomains = [
            'bit.ly/malware',
            'suspicious-site.com',
            // Add more malicious patterns as needed
        ];

        foreach ($maliciousDomains as $domain) {
            if (strpos($url, $domain) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get URL statistics.
     */
    public function getUrlStats(Url $url): array
    {
        return [
            'total_clicks' => $url->click_count,
            'unique_clicks' => $url->analytics()->distinct('ip_address')->count(),
            'clicks_today' => $url->analytics()->whereDate('clicked_at', today())->count(),
            'clicks_this_week' => $url->analytics()->whereBetween('clicked_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
            'clicks_this_month' => $url->analytics()->whereMonth('clicked_at', now()->month)->count(),
            'top_countries' => $url->analytics()
                ->selectRaw('country_name, COUNT(*) as count')
                ->whereNotNull('country_name')
                ->groupBy('country_name')
                ->orderByDesc('count')
                ->limit(5)
                ->get(),
            'top_devices' => $url->analytics()
                ->selectRaw('device_type, COUNT(*) as count')
                ->whereNotNull('device_type')
                ->groupBy('device_type')
                ->orderByDesc('count')
                ->get(),
            'top_browsers' => $url->analytics()
                ->selectRaw('browser, COUNT(*) as count')
                ->whereNotNull('browser')
                ->groupBy('browser')
                ->orderByDesc('count')
                ->limit(5)
                ->get(),
        ];
    }

    /**
     * Bulk shorten URLs.
     */
    public function bulkShortenUrls(array $urls, ?User $user = null): array
    {
        $results = [];

        foreach ($urls as $index => $urlData) {
            try {
                $results[$index] = [
                    'success' => true,
                    'url' => $this->shortenUrl($urlData, $user),
                ];
            } catch (\Exception $e) {
                $results[$index] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'original_url' => $urlData['original_url'] ?? 'Unknown',
                ];
            }
        }

        return $results;
    }
}
