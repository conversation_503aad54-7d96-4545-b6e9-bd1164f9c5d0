<?php

namespace App\Services;

use App\Models\Url;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Response;

class QrCodeService
{
    /**
     * Generate QR code for URL with customization options.
     */
    public function generateQrCode(Url $url, array $options = []): string
    {
        $size = $options['size'] ?? 300;
        $margin = $options['margin'] ?? 2;
        $format = $options['format'] ?? 'png';
        $errorCorrection = $options['error_correction'] ?? 'M';
        $backgroundColor = $options['background_color'] ?? [255, 255, 255];
        $foregroundColor = $options['foreground_color'] ?? [0, 0, 0];

        try {
            $qrCode = QrCode::format($format)
                ->size($size)
                ->margin($margin)
                ->errorCorrection($errorCorrection)
                ->backgroundColor($backgroundColor[0], $backgroundColor[1], $backgroundColor[2])
                ->color($foregroundColor[0], $foregroundColor[1], $foregroundColor[2])
                ->generate($url->short_url);

            $filename = 'qr-codes/' . $url->id . '_' . time() . '.' . $format;
            Storage::disk('public')->put($filename, $qrCode);

            // Update URL record with new QR code path
            $url->update(['qr_code_path' => $filename]);

            return $filename;
        } catch (\Exception $e) {
            \Log::error('QR Code generation failed: ' . $e->getMessage());
            throw new \Exception('Failed to generate QR code');
        }
    }

    /**
     * Generate QR code with logo overlay.
     */
    public function generateQrCodeWithLogo(Url $url, string $logoPath, array $options = []): string
    {
        $size = $options['size'] ?? 300;
        $margin = $options['margin'] ?? 2;
        $format = $options['format'] ?? 'png';
        $logoSize = $options['logo_size'] ?? 60;

        try {
            // Generate base QR code
            $qrCode = QrCode::format($format)
                ->size($size)
                ->margin($margin)
                ->errorCorrection('H') // High error correction for logo overlay
                ->generate($url->short_url);

            // Create image resources
            $qrImage = imagecreatefromstring($qrCode);
            
            if (!$qrImage) {
                throw new \Exception('Failed to create QR code image');
            }

            // Load logo
            $logoImage = $this->loadLogoImage($logoPath);
            if (!$logoImage) {
                throw new \Exception('Failed to load logo image');
            }

            // Resize logo
            $logoResized = imagecreatetruecolor($logoSize, $logoSize);
            $logoWidth = imagesx($logoImage);
            $logoHeight = imagesy($logoImage);
            
            imagecopyresampled($logoResized, $logoImage, 0, 0, 0, 0, $logoSize, $logoSize, $logoWidth, $logoHeight);

            // Calculate position to center logo
            $qrWidth = imagesx($qrImage);
            $qrHeight = imagesy($qrImage);
            $logoX = ($qrWidth - $logoSize) / 2;
            $logoY = ($qrHeight - $logoSize) / 2;

            // Add white background circle for logo
            $white = imagecolorallocate($qrImage, 255, 255, 255);
            imagefilledellipse($qrImage, $logoX + $logoSize/2, $logoY + $logoSize/2, $logoSize + 10, $logoSize + 10, $white);

            // Overlay logo on QR code
            imagecopy($qrImage, $logoResized, $logoX, $logoY, 0, 0, $logoSize, $logoSize);

            // Save the final image
            ob_start();
            imagepng($qrImage);
            $finalImage = ob_get_contents();
            ob_end_clean();

            $filename = 'qr-codes/' . $url->id . '_logo_' . time() . '.png';
            Storage::disk('public')->put($filename, $finalImage);

            // Clean up memory
            imagedestroy($qrImage);
            imagedestroy($logoImage);
            imagedestroy($logoResized);

            // Update URL record
            $url->update(['qr_code_path' => $filename]);

            return $filename;
        } catch (\Exception $e) {
            \Log::error('QR Code with logo generation failed: ' . $e->getMessage());
            throw new \Exception('Failed to generate QR code with logo');
        }
    }

    /**
     * Load logo image from various formats.
     */
    private function loadLogoImage(string $logoPath)
    {
        $imageInfo = getimagesize($logoPath);
        if (!$imageInfo) {
            return false;
        }

        switch ($imageInfo['mime']) {
            case 'image/jpeg':
                return imagecreatefromjpeg($logoPath);
            case 'image/png':
                return imagecreatefrompng($logoPath);
            case 'image/gif':
                return imagecreatefromgif($logoPath);
            default:
                return false;
        }
    }

    /**
     * Generate QR code with custom styling.
     */
    public function generateStyledQrCode(Url $url, array $style = []): string
    {
        $size = $style['size'] ?? 300;
        $margin = $style['margin'] ?? 2;
        $format = $style['format'] ?? 'png';
        
        // Color options
        $backgroundColor = $style['background_color'] ?? [255, 255, 255];
        $foregroundColor = $style['foreground_color'] ?? [0, 0, 0];
        
        // Gradient options (if supported)
        $useGradient = $style['use_gradient'] ?? false;
        $gradientStart = $style['gradient_start'] ?? [0, 0, 255];
        $gradientEnd = $style['gradient_end'] ?? [255, 0, 0];

        try {
            $qrCodeBuilder = QrCode::format($format)
                ->size($size)
                ->margin($margin)
                ->errorCorrection('M')
                ->backgroundColor($backgroundColor[0], $backgroundColor[1], $backgroundColor[2]);

            if (!$useGradient) {
                $qrCodeBuilder->color($foregroundColor[0], $foregroundColor[1], $foregroundColor[2]);
            }

            $qrCode = $qrCodeBuilder->generate($url->short_url);

            // Apply gradient if requested (basic implementation)
            if ($useGradient) {
                $qrCode = $this->applyGradient($qrCode, $gradientStart, $gradientEnd);
            }

            $filename = 'qr-codes/' . $url->id . '_styled_' . time() . '.' . $format;
            Storage::disk('public')->put($filename, $qrCode);

            $url->update(['qr_code_path' => $filename]);

            return $filename;
        } catch (\Exception $e) {
            \Log::error('Styled QR Code generation failed: ' . $e->getMessage());
            throw new \Exception('Failed to generate styled QR code');
        }
    }

    /**
     * Apply gradient effect to QR code (basic implementation).
     */
    private function applyGradient($qrCodeData, array $startColor, array $endColor): string
    {
        // This is a simplified gradient implementation
        // In a production environment, you might want to use more sophisticated image processing
        return $qrCodeData;
    }

    /**
     * Get QR code response for download.
     */
    public function getQrCodeResponse(Url $url, array $options = []): Response
    {
        $format = $options['format'] ?? 'png';
        $download = $options['download'] ?? false;
        
        // Check if QR code exists, generate if not
        if (!$url->qr_code_path || !Storage::disk('public')->exists($url->qr_code_path)) {
            $this->generateQrCode($url, $options);
        }

        $qrCodeData = Storage::disk('public')->get($url->qr_code_path);
        $filename = ($url->title ? \Str::slug($url->title) : 'qr-code') . '.' . $format;

        $headers = [
            'Content-Type' => 'image/' . $format,
        ];

        if ($download) {
            $headers['Content-Disposition'] = 'attachment; filename="' . $filename . '"';
        } else {
            $headers['Content-Disposition'] = 'inline; filename="' . $filename . '"';
        }

        return response($qrCodeData, 200, $headers);
    }

    /**
     * Generate QR code in multiple formats.
     */
    public function generateMultipleFormats(Url $url, array $formats = ['png', 'svg']): array
    {
        $results = [];
        
        foreach ($formats as $format) {
            try {
                $options = ['format' => $format];
                $filename = $this->generateQrCode($url, $options);
                $results[$format] = [
                    'success' => true,
                    'filename' => $filename,
                    'url' => Storage::disk('public')->url($filename)
                ];
            } catch (\Exception $e) {
                $results[$format] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Get QR code statistics.
     */
    public function getQrCodeStats(Url $url): array
    {
        return [
            'has_qr_code' => !empty($url->qr_code_path),
            'qr_code_exists' => $url->qr_code_path && Storage::disk('public')->exists($url->qr_code_path),
            'qr_code_size' => $url->qr_code_path ? Storage::disk('public')->size($url->qr_code_path) : 0,
            'qr_code_url' => $url->qr_code_path ? Storage::disk('public')->url($url->qr_code_path) : null,
            'created_at' => $url->qr_code_path ? Storage::disk('public')->lastModified($url->qr_code_path) : null,
        ];
    }

    /**
     * Delete QR code file.
     */
    public function deleteQrCode(Url $url): bool
    {
        if ($url->qr_code_path && Storage::disk('public')->exists($url->qr_code_path)) {
            $deleted = Storage::disk('public')->delete($url->qr_code_path);
            if ($deleted) {
                $url->update(['qr_code_path' => null]);
            }
            return $deleted;
        }
        
        return true;
    }
}
