@extends('layouts.app')

@section('title', 'URL Analytics')

@section('content')
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1><i class="fas fa-chart-bar"></i> URL Analytics</h1>
                    <p class="text-muted mb-0">{{ $url->title ?: 'Untitled URL' }}</p>
                </div>
                <div>
                    <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <a href="{{ route('urls.edit', $url) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit URL
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- URL Info Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Original URL</h6>
                            <p class="mb-3">
                                <a href="{{ $url->original_url }}" target="_blank" class="text-break">
                                    {{ $url->original_url }}
                                    <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Short URL</h6>
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" value="{{ $url->short_url }}" readonly>
                                <button class="btn btn-outline-secondary copy-btn" onclick="copyToClipboard('{{ $url->short_url }}')">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-qrcode"></i> QR
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{{ route('qr.show', $url->custom_alias ?: $url->short_code) }}" target="_blank">
                                            <i class="fas fa-eye"></i> View QR Code
                                        </a></li>
                                        <li><a class="dropdown-item" href="{{ route('qr.download', $url->custom_alias ?: $url->short_code) }}">
                                            <i class="fas fa-download"></i> Download QR Code
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="{{ route('qr.customize', $url) }}">
                                            <i class="fas fa-palette"></i> Customize QR Code
                                        </a></li>
                                    </ul>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-share-alt"></i> Share
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="shareOnPlatform('twitter')">
                                            <i class="fab fa-twitter text-primary"></i> Twitter
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="shareOnPlatform('facebook')">
                                            <i class="fab fa-facebook text-primary"></i> Facebook
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="shareOnPlatform('linkedin')">
                                            <i class="fab fa-linkedin text-primary"></i> LinkedIn
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="shareOnPlatform('whatsapp')">
                                            <i class="fab fa-whatsapp text-success"></i> WhatsApp
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="shareOnPlatform('telegram')">
                                            <i class="fab fa-telegram text-info"></i> Telegram
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#" onclick="shareOnPlatform('email')">
                                            <i class="fas fa-envelope text-secondary"></i> Email
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <h6 class="text-muted">Created</h6>
                            <p>{{ $url->created_at->format('M d, Y H:i') }}</p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-muted">Status</h6>
                            <p>
                                @if($url->isExpired())
                                    <span class="badge bg-warning">Expired</span>
                                @else
                                    <span class="badge bg-success">Active</span>
                                @endif
                                
                                @if($url->isPasswordProtected())
                                    <span class="badge bg-info">Protected</span>
                                @endif
                            </p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-muted">Expires</h6>
                            <p>{{ $url->expires_at ? $url->expires_at->format('M d, Y H:i') : 'Never' }}</p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-muted">Total Clicks</h6>
                            <p><strong class="text-primary">{{ number_format($url->click_count) }}</strong></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Overview -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-0">{{ number_format($analytics['total_clicks']) }}</h3>
                    <p class="mb-0">Total Clicks</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3 class="mb-0">{{ number_format($analytics['unique_visitors']) }}</h3>
                    <p class="mb-0">Unique Visitors</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3 class="mb-0">{{ count($analytics['countries']) }}</h3>
                    <p class="mb-0">Countries</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3 class="mb-0">{{ count($analytics['devices']) }}</h3>
                    <p class="mb-0">Device Types</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Daily Clicks Chart -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> Daily Clicks (Last 30 Days)</h5>
                </div>
                <div class="card-body">
                    <canvas id="dailyClicksChart" class="analytics-chart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Device Types Chart -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-mobile-alt"></i> Device Types</h5>
                </div>
                <div class="card-body">
                    <canvas id="deviceChart" class="analytics-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analytics -->
    <div class="row">
        <!-- Countries -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-globe"></i> Top Countries</h5>
                </div>
                <div class="card-body">
                    @if(count($analytics['countries']) > 0)
                        @foreach($analytics['countries'] as $country)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>{{ $country['country'] }}</span>
                            <div>
                                <span class="badge bg-primary">{{ $country['count'] }}</span>
                                <small class="text-muted">({{ $country['percentage'] }}%)</small>
                            </div>
                        </div>
                        <div class="progress mb-3" style="height: 6px;">
                            <div class="progress-bar" style="width: {{ $country['percentage'] }}%"></div>
                        </div>
                        @endforeach
                    @else
                        <p class="text-muted text-center">No country data available</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Browsers -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-browser"></i> Top Browsers</h5>
                </div>
                <div class="card-body">
                    @if(count($analytics['browsers']) > 0)
                        @foreach($analytics['browsers'] as $browser)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>{{ $browser['browser'] }}</span>
                            <div>
                                <span class="badge bg-info">{{ $browser['count'] }}</span>
                                <small class="text-muted">({{ $browser['percentage'] }}%)</small>
                            </div>
                        </div>
                        <div class="progress mb-3" style="height: 6px;">
                            <div class="progress-bar bg-info" style="width: {{ $browser['percentage'] }}%"></div>
                        </div>
                        @endforeach
                    @else
                        <p class="text-muted text-center">No browser data available</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Platforms -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-desktop"></i> Top Platforms</h5>
                </div>
                <div class="card-body">
                    @if(count($analytics['platforms']) > 0)
                        @foreach($analytics['platforms'] as $platform)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>{{ $platform['platform'] }}</span>
                            <div>
                                <span class="badge bg-success">{{ $platform['count'] }}</span>
                                <small class="text-muted">({{ $platform['percentage'] }}%)</small>
                            </div>
                        </div>
                        <div class="progress mb-3" style="height: 6px;">
                            <div class="progress-bar bg-success" style="width: {{ $platform['percentage'] }}%"></div>
                        </div>
                        @endforeach
                    @else
                        <p class="text-muted text-center">No platform data available</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Referrers -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-external-link-alt"></i> Top Referrers</h5>
                </div>
                <div class="card-body">
                    @if(count($analytics['referrers']) > 0)
                        @foreach($analytics['referrers'] as $referrer)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>{{ $referrer['referrer'] }}</span>
                            <div>
                                <span class="badge bg-warning">{{ $referrer['count'] }}</span>
                                <small class="text-muted">({{ $referrer['percentage'] }}%)</small>
                            </div>
                        </div>
                        <div class="progress mb-3" style="height: 6px;">
                            <div class="progress-bar bg-warning" style="width: {{ $referrer['percentage'] }}%"></div>
                        </div>
                        @endforeach
                    @else
                        <p class="text-muted text-center">No referrer data available</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Social sharing functionality
function shareOnPlatform(platform) {
    const url = encodeURIComponent('{{ $url->short_url }}');
    const title = encodeURIComponent('{{ $url->title ?: "Check out this link" }}');
    const text = encodeURIComponent('{{ $url->title ?: "Check out this link" }} {{ $url->short_url }}');

    let shareUrl = '';

    switch(platform) {
        case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}&hashtags=minilink`;
            break;
        case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
            break;
        case 'linkedin':
            shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}`;
            break;
        case 'whatsapp':
            shareUrl = `https://wa.me/?text=${text}`;
            break;
        case 'telegram':
            shareUrl = `https://t.me/share/url?url=${url}&text=${title}`;
            break;
        case 'email':
            shareUrl = `mailto:?subject=${title}&body=Check out this link: {{ $url->short_url }}`;
            break;
    }

    if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400');
    }
}

// Daily Clicks Chart
const dailyClicksCtx = document.getElementById('dailyClicksChart').getContext('2d');
const dailyClicksChart = new Chart(dailyClicksCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode(array_column($analytics['daily_clicks'], 'date')) !!},
        datasets: [{
            label: 'Clicks',
            data: {!! json_encode(array_column($analytics['daily_clicks'], 'clicks')) !!},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Device Types Chart
@if(count($analytics['devices']) > 0)
const deviceCtx = document.getElementById('deviceChart').getContext('2d');
const deviceChart = new Chart(deviceCtx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode(array_column($analytics['devices'], 'device')) !!},
        datasets: [{
            data: {!! json_encode(array_column($analytics['devices'], 'count')) !!},
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
@endif
</script>
@endpush
