@extends('layouts.app')

@section('title', 'Password Protected URL')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body p-5 text-center">
                    <div class="mb-4">
                        <i class="fas fa-lock fa-3x text-warning"></i>
                    </div>
                    
                    <h3 class="mb-3">Password Protected URL</h3>
                    <p class="text-muted mb-4">
                        This URL is password protected. Please enter the password to continue.
                    </p>

                    <form method="POST" action="{{ request()->url() }}">
                        @csrf
                        <div class="mb-3">
                            <input type="password" 
                                   class="form-control form-control-lg text-center @error('password') is-invalid @enderror" 
                                   name="password" 
                                   placeholder="Enter password"
                                   required 
                                   autofocus>
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-unlock"></i> Access URL
                        </button>
                    </form>
                    
                    <div class="mt-4">
                        <small class="text-muted">
                            Don't have the password? Contact the person who shared this link.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
