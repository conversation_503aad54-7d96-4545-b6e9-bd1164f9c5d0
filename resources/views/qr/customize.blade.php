@extends('layouts.app')

@section('title', 'Customize QR Code')

@section('content')
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1><i class="fas fa-qrcode"></i> Customize QR Code</h1>
                    <p class="text-muted mb-0">{{ $url->title ?: 'Untitled URL' }}</p>
                </div>
                <div>
                    <a href="{{ route('urls.show', $url) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Analytics
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- QR Code Preview -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-eye"></i> QR Code Preview</h5>
                </div>
                <div class="card-body text-center">
                    <div id="qrPreview" class="mb-3">
                        @if($qrStats['qr_code_exists'])
                            <img src="{{ asset('storage/' . $url->qr_code_path) }}" 
                                 alt="QR Code" 
                                 class="img-fluid" 
                                 style="max-width: 300px;">
                        @else
                            <div class="text-muted py-5">
                                <i class="fas fa-qrcode fa-3x mb-3"></i>
                                <p>No QR code generated yet</p>
                            </div>
                        @endif
                    </div>
                    
                    <div class="btn-group" role="group">
                        <a href="{{ route('qr.show', $url->custom_alias ?: $url->short_code) }}" 
                           class="btn btn-outline-primary" 
                           target="_blank">
                            <i class="fas fa-external-link-alt"></i> View
                        </a>
                        <a href="{{ route('qr.download', $url->custom_alias ?: $url->short_code) }}" 
                           class="btn btn-outline-success">
                            <i class="fas fa-download"></i> Download
                        </a>
                    </div>
                </div>
            </div>

            <!-- QR Code Stats -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> QR Code Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <strong>Status:</strong>
                            @if($qrStats['qr_code_exists'])
                                <span class="badge bg-success">Generated</span>
                            @else
                                <span class="badge bg-warning">Not Generated</span>
                            @endif
                        </div>
                        <div class="col-6">
                            <strong>Size:</strong>
                            {{ $qrStats['qr_code_size'] ? number_format($qrStats['qr_code_size'] / 1024, 1) . ' KB' : 'N/A' }}
                        </div>
                    </div>
                    @if($qrStats['created_at'])
                    <div class="row mt-2">
                        <div class="col-12">
                            <strong>Created:</strong>
                            {{ date('M d, Y H:i', $qrStats['created_at']) }}
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Customization Options -->
        <div class="col-lg-6">
            <!-- Basic Customization -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-palette"></i> Basic Customization</h5>
                </div>
                <div class="card-body">
                    <form id="qrCustomizeForm">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="size" class="form-label">Size (px)</label>
                                    <input type="range" 
                                           class="form-range" 
                                           id="size" 
                                           name="size" 
                                           min="100" 
                                           max="1000" 
                                           value="300" 
                                           step="50">
                                    <div class="d-flex justify-content-between">
                                        <small>100px</small>
                                        <span id="sizeValue">300px</span>
                                        <small>1000px</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="margin" class="form-label">Margin</label>
                                    <input type="range" 
                                           class="form-range" 
                                           id="margin" 
                                           name="margin" 
                                           min="0" 
                                           max="10" 
                                           value="2" 
                                           step="1">
                                    <div class="d-flex justify-content-between">
                                        <small>0</small>
                                        <span id="marginValue">2</span>
                                        <small>10</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="format" class="form-label">Format</label>
                                    <select class="form-select" id="format" name="format">
                                        <option value="png">PNG</option>
                                        <option value="svg">SVG</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="errorCorrection" class="form-label">Error Correction</label>
                                    <select class="form-select" id="errorCorrection" name="error_correction">
                                        <option value="L">Low (7%)</option>
                                        <option value="M" selected>Medium (15%)</option>
                                        <option value="Q">Quartile (25%)</option>
                                        <option value="H">High (30%)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="backgroundColor" class="form-label">Background Color</label>
                                    <input type="color" 
                                           class="form-control form-control-color" 
                                           id="backgroundColor" 
                                           value="#ffffff">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="foregroundColor" class="form-label">Foreground Color</label>
                                    <input type="color" 
                                           class="form-control form-control-color" 
                                           id="foregroundColor" 
                                           value="#000000">
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-magic"></i> Generate QR Code
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Logo Upload -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-image"></i> Add Logo</h5>
                </div>
                <div class="card-body">
                    <form id="qrLogoForm" enctype="multipart/form-data">
                        @csrf
                        <div class="mb-3">
                            <label for="logo" class="form-label">Upload Logo</label>
                            <input type="file" 
                                   class="form-control" 
                                   id="logo" 
                                   name="logo" 
                                   accept="image/*">
                            <div class="form-text">Recommended: Square image, max 2MB</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="logoSize" class="form-label">Logo Size</label>
                                    <input type="range" 
                                           class="form-range" 
                                           id="logoSize" 
                                           name="logo_size" 
                                           min="20" 
                                           max="200" 
                                           value="60" 
                                           step="10">
                                    <div class="d-flex justify-content-between">
                                        <small>20px</small>
                                        <span id="logoSizeValue">60px</span>
                                        <small>200px</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="qrSizeForLogo" class="form-label">QR Size</label>
                                    <input type="range" 
                                           class="form-range" 
                                           id="qrSizeForLogo" 
                                           name="size" 
                                           min="200" 
                                           max="800" 
                                           value="300" 
                                           step="50">
                                    <div class="d-flex justify-content-between">
                                        <small>200px</small>
                                        <span id="qrSizeForLogoValue">300px</span>
                                        <small>800px</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus"></i> Generate with Logo
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Multiple Formats -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-layer-group"></i> Multiple Formats</h5>
                </div>
                <div class="card-body">
                    <form id="qrMultipleForm">
                        @csrf
                        <div class="mb-3">
                            <label class="form-label">Select Formats</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="png" id="formatPng" checked>
                                <label class="form-check-label" for="formatPng">PNG</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="svg" id="formatSvg">
                                <label class="form-check-label" for="formatSvg">SVG</label>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-download"></i> Generate Multiple Formats
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Update range input values
document.getElementById('size').addEventListener('input', function() {
    document.getElementById('sizeValue').textContent = this.value + 'px';
});

document.getElementById('margin').addEventListener('input', function() {
    document.getElementById('marginValue').textContent = this.value;
});

document.getElementById('logoSize').addEventListener('input', function() {
    document.getElementById('logoSizeValue').textContent = this.value + 'px';
});

document.getElementById('qrSizeForLogo').addEventListener('input', function() {
    document.getElementById('qrSizeForLogoValue').textContent = this.value + 'px';
});

// Basic QR Code Generation
document.getElementById('qrCustomizeForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);
    formData.append('size', document.getElementById('size').value);
    formData.append('margin', document.getElementById('margin').value);
    formData.append('format', document.getElementById('format').value);
    formData.append('error_correction', document.getElementById('errorCorrection').value);
    
    // Convert hex colors to RGB arrays
    const bgColor = hexToRgb(document.getElementById('backgroundColor').value);
    const fgColor = hexToRgb(document.getElementById('foregroundColor').value);
    
    formData.append('background_color[0]', bgColor.r);
    formData.append('background_color[1]', bgColor.g);
    formData.append('background_color[2]', bgColor.b);
    formData.append('foreground_color[0]', fgColor.r);
    formData.append('foreground_color[1]', fgColor.g);
    formData.append('foreground_color[2]', fgColor.b);
    
    generateQrCode(formData, '{{ route("qr.generate", $url) }}');
});

// QR Code with Logo
document.getElementById('qrLogoForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);
    
    generateQrCode(formData, '{{ route("qr.generate-logo", $url) }}');
});

// Multiple Formats
document.getElementById('qrMultipleForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formats = [];
    if (document.getElementById('formatPng').checked) formats.push('png');
    if (document.getElementById('formatSvg').checked) formats.push('svg');
    
    const formData = new FormData();
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);
    formats.forEach(format => formData.append('formats[]', format));
    
    generateQrCode(formData, '{{ route("qr.generate-multiple", $url) }}');
});

function generateQrCode(formData, url) {
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    submitBtn.disabled = true;
    
    fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateQrPreview(data.data);
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'An error occurred while generating the QR code.');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function updateQrPreview(data) {
    const preview = document.getElementById('qrPreview');
    
    if (data.url) {
        preview.innerHTML = `<img src="${data.url}" alt="QR Code" class="img-fluid" style="max-width: 300px;">`;
    } else if (data.png && data.png.url) {
        preview.innerHTML = `<img src="${data.png.url}" alt="QR Code" class="img-fluid" style="max-width: 300px;">`;
    }
}

function hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
    } : null;
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 end-0 m-3`;
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
</script>
@endpush
