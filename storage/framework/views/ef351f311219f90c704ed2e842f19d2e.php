<?php $__env->startSection('title', 'URL Shortener'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fas fa-link"></i> Shorten Your URLs
                </h1>
                <p class="lead mb-5">
                    Create short, memorable links that are easy to share. Track clicks, analyze performance, and manage your URLs with powerful analytics.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- URL Shortening Form -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-lg">
                    <div class="card-body p-5">
                        <form id="shortenForm" action="<?php echo e(route('urls.store')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="original_url" class="form-label">Enter your long URL</label>
                                        <input type="url" 
                                               class="form-control form-control-lg <?php $__errorArgs = ['original_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="original_url" 
                                               name="original_url" 
                                               placeholder="https://example.com/very-long-url"
                                               value="<?php echo e(old('original_url')); ?>"
                                               required>
                                        <?php $__errorArgs = ['original_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-magic"></i> Shorten URL
                                    </button>
                                </div>
                            </div>

                            <!-- Advanced Options (Collapsible) -->
                            <div class="mt-3">
                                <button class="btn btn-link p-0" type="button" data-bs-toggle="collapse" data-bs-target="#advancedOptions">
                                    <i class="fas fa-cog"></i> Advanced Options
                                </button>
                            </div>

                            <div class="collapse mt-3" id="advancedOptions">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="custom_alias" class="form-label">Custom Alias (Optional)</label>
                                            <input type="text" 
                                                   class="form-control <?php $__errorArgs = ['custom_alias'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                   id="custom_alias" 
                                                   name="custom_alias" 
                                                   placeholder="my-custom-link"
                                                   value="<?php echo e(old('custom_alias')); ?>">
                                            <div class="form-text">Leave empty for auto-generated short code</div>
                                            <?php $__errorArgs = ['custom_alias'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Title (Optional)</label>
                                            <input type="text" 
                                                   class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                   id="title" 
                                                   name="title" 
                                                   placeholder="My Link Title"
                                                   value="<?php echo e(old('title')); ?>">
                                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if(auth()->guard()->check()): ?>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="expires_at" class="form-label">Expiration Date (Optional)</label>
                                            <input type="datetime-local" 
                                                   class="form-control <?php $__errorArgs = ['expires_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                   id="expires_at" 
                                                   name="expires_at" 
                                                   value="<?php echo e(old('expires_at')); ?>">
                                            <?php $__errorArgs = ['expires_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password" class="form-label">Password Protection (Optional)</label>
                                            <input type="password" 
                                                   class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                   id="password" 
                                                   name="password" 
                                                   placeholder="Enter password">
                                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </form>

                        <!-- Result Section -->
                        <div id="result" class="d-none">
                            <hr class="my-4">
                            <div class="short-url-result">
                                <h5><i class="fas fa-check-circle text-success"></i> Your shortened URL is ready!</h5>
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="shortUrl" readonly>
                                            <button class="btn btn-outline-secondary copy-btn" type="button" onclick="copyShortUrl()">
                                                <i class="fas fa-copy"></i> Copy
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <a href="#" id="qrCodeLink" class="btn btn-outline-primary me-2" target="_blank">
                                            <i class="fas fa-qrcode"></i> QR Code
                                        </a>
                                        <a href="#" id="analyticsLink" class="btn btn-outline-info">
                                            <i class="fas fa-chart-bar"></i> Analytics
                                        </a>
                                    </div>
                                </div>
                                
                                <!-- Social Sharing -->
                                <div class="mt-3">
                                    <h6>Share on social media:</h6>
                                    <div class="btn-group" role="group">
                                        <a href="#" id="shareTwitter" class="btn btn-outline-primary btn-sm" target="_blank">
                                            <i class="fab fa-twitter"></i> Twitter
                                        </a>
                                        <a href="#" id="shareFacebook" class="btn btn-outline-primary btn-sm" target="_blank">
                                            <i class="fab fa-facebook"></i> Facebook
                                        </a>
                                        <a href="#" id="shareLinkedIn" class="btn btn-outline-primary btn-sm" target="_blank">
                                            <i class="fab fa-linkedin"></i> LinkedIn
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-lg-12">
                <h2 class="fw-bold">Why Choose Minilink?</h2>
                <p class="text-muted">Powerful features to help you manage and track your links</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-tachometer-alt fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">Lightning Fast</h5>
                        <p class="card-text">Generate short URLs instantly with our optimized infrastructure.</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-chart-line fa-3x text-success"></i>
                        </div>
                        <h5 class="card-title">Detailed Analytics</h5>
                        <p class="card-text">Track clicks, locations, devices, and more with comprehensive analytics.</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-qrcode fa-3x text-info"></i>
                        </div>
                        <h5 class="card-title">QR Codes</h5>
                        <p class="card-text">Automatically generate QR codes for easy mobile sharing.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php if(!auth()->check()): ?>
<!-- CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h2 class="fw-bold mb-4">Ready to get started?</h2>
                <p class="lead mb-4">Join thousands of users who trust Minilink for their URL shortening needs.</p>
                <a href="<?php echo e(route('register')); ?>" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-user-plus"></i> Sign Up Free
                </a>
                <a href="<?php echo e(route('login')); ?>" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.getElementById('shortenForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Shortening...';
    submitBtn.disabled = true;
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showResult(data.data);
        } else {
            showErrors(data.errors);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred. Please try again.');
    })
    .finally(() => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

function showResult(data) {
    document.getElementById('shortUrl').value = data.short_url;
    document.getElementById('qrCodeLink').href = data.short_url + '/qr';
    document.getElementById('analyticsLink').href = '/urls/' + data.id;
    
    // Setup social sharing
    const shortUrl = encodeURIComponent(data.short_url);
    document.getElementById('shareTwitter').href = `https://twitter.com/intent/tweet?url=${shortUrl}`;
    document.getElementById('shareFacebook').href = `https://www.facebook.com/sharer/sharer.php?u=${shortUrl}`;
    document.getElementById('shareLinkedIn').href = `https://www.linkedin.com/sharing/share-offsite/?url=${shortUrl}`;
    
    document.getElementById('result').classList.remove('d-none');
}

function showErrors(errors) {
    // Clear previous errors
    document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
    document.querySelectorAll('.invalid-feedback').forEach(el => el.remove());
    
    // Show new errors
    for (const [field, messages] of Object.entries(errors)) {
        const input = document.querySelector(`[name="${field}"]`);
        if (input) {
            input.classList.add('is-invalid');
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = messages[0];
            input.parentNode.appendChild(feedback);
        }
    }
}

function copyShortUrl() {
    const shortUrl = document.getElementById('shortUrl').value;
    copyToClipboard(shortUrl);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Desktop/S.A.R.L/My projects/minilinkatphp/resources/views/urls/index.blade.php ENDPATH**/ ?>